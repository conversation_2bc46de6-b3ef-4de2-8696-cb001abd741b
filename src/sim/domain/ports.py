from abc import ABC, abstractmethod

from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import IMSI, Month
from sim.domain import model
from sim.domain.model import MSISDN, MarketShareModel, SIMProviderLog, SimStatus


class AbstractSIMProvisioningAPI(ABC):
    @abstractmethod
    def sim_status(self, imsi: IMSI, msisdn_: MSISDN) -> model.SIMStatusResponse:
        ...

    @abstractmethod
    def activate_sim(
        self,
        imsi: IMSI,
        msisdn_: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        ...

    @abstractmethod
    def suspend_sim(
        self,
        msisdn_: MSISDN,
        prior_status: SimStatus,
        client_ip: str | None = None,
    ) -> SIMProviderLog:
        ...

    @abstractmethod
    def sim_workitem_status(self, work_id: str) -> model.SIMWorkItemStatusResponse:
        ...


class AbstractMarketShareAPI(ABC):
    @abstractmethod
    def market_share_usage(
        self, request_data: MarketShareModel
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def get_imsis_usage(self, marketshare: model.MarketShareModel) -> model.MarketShare:
        ...


class AbstractAuditService(ABC):
    @abstractmethod
    def add_reallocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_allocation_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_sim_audit_api(self, audit_details: list[model.SIMCardAudit]):
        ...

    @abstractmethod
    def add_sim_provider_audit_api(self, audit_details: model.SIMCardProviderAudit):
        ...

    @abstractmethod
    def get_sim_pending_info_audit_api(self, imsi: IMSI):
        ...

    @abstractmethod
    def get_sim_provider_log_audit_api(self, work_id: str):
        ...

    @abstractmethod
    def get_sim_audit_api(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    @abstractmethod
    def get_account_audit_api(
        self,
        imsi: list[IMSI],
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        ...

    @abstractmethod
    def validate_notification(
        self, sim_provider_log: model.SIMProviderLog, sim_status: str
    ) -> bool:
        ...

    @abstractmethod
    def add_upload_msisdn_audit_api(
        self, audit_details: list[model.MsisdnPoolAudit] | list[model.SIMCardAudit]
    ):
        ...

    @abstractmethod
    def get_sim_msisdn_update_log_audit_api(self):
        ...
