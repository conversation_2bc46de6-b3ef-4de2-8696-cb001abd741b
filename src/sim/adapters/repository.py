from abc import ABC, abstractmethod
from collections import defaultdict
from datetime import datetime
from operator import attrgetter
from typing import Any, Iterable, Iterator

from more_itertools import ilen
from sqlalchemy import (
    and_,
    asc,
    bindparam,
    case,
    desc,
    exists,
    func,
    literal,
    nulls_first,
    nulls_last,
    or_,
    select,
    update,
)
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session, aliased
from sqlalchemy.sql.functions import count

from app.config import logger
from cdrdata.adapters import orm as cdrdata_orm
from common.ordering import Ordering, OrderingResolver
from common.pagination import Pagination
from common.searching import Searching, apply_search_to_sql
from common.types import ICCID, IMSI, MSISDN, FormFactor, Month, SimStatus
from rate_plans.adapters import orm as rate_plan_orm
from sim.adapters import orm
from sim.domain import model
from sim.exceptions import (
    IMSIDoesNotExit,
    NoSimFound,
    RangeIntegrityError,
    UnallocationException,
)


class AbstractSimRepository(ABC):
    @abstractmethod
    def add_range(
        self, range_: model.Range, msisdn_pool: list[model.MsisdnPool] | None = None
    ) -> None:
        ...

    @abstractmethod
    def get_range_list(self) -> Iterator[model.Range]:
        ...

    @abstractmethod
    def check_imsi_range(self, imsi_first: IMSI, imsi_last: IMSI) -> int | None:
        ...

    @abstractmethod
    def get_range_by_id(self, range_id: int) -> model.Range | None:
        ...

    @abstractmethod
    def update_range(self, range_: model.Range) -> None:
        ...

    @abstractmethod
    def get_allocation_by_id(self, allocation_id) -> model.Allocation | None:
        ...

    @abstractmethod
    def get_last_allocation_in_range(self, range_id: int) -> model.Allocation | None:
        ...

    @abstractmethod
    def add_allocation(self, allocation: model.Allocation) -> None:
        # TODO: remove deprecated method
        # Used only in tests
        ...

    @abstractmethod
    def remove_allocations_in_range(self, range: model.Range) -> None:
        ...

    @abstractmethod
    def remove_allocation(self, allocation: model.Allocation) -> None:
        ...

    @abstractmethod
    def remove_range(self, range: model.Range) -> None:
        ...

    @abstractmethod
    def update_sim_cards_with_allocation(self, allocation: model.Allocation) -> None:
        ...

    @abstractmethod
    def get_sim_cards(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        ...

    @abstractmethod
    def get_sim_count(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: model.SimStatus | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_sim_remains(self) -> Iterator[tuple[FormFactor, int]]:
        ...

    @abstractmethod
    def update_sim_card(self, imsi: IMSI, sim_status: model.SimStatus) -> None:
        ...

    @abstractmethod
    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        ...

    @abstractmethod
    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterator[model.SimUsage]:
        ...

    @abstractmethod
    def get_sim_usage_count(
        self, account_id: int | None = None, searching: Searching | None = None
    ) -> tuple[int, int, int, int, int]:
        ...

    @abstractmethod
    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> Iterator[model.ActiveSimMonthlyStatistic]:
        ...

    @abstractmethod
    def cards_active_statistic_count(self, account_id: int, month: Month) -> int:
        ...

    @abstractmethod
    def add_sim_monthly_status(
        self,
        sim_monthly_status: model.SIMMonthlyStatus,
    ) -> int:
        ...

    @abstractmethod
    def check_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month | None,
    ) -> bool:
        ...

    @abstractmethod
    def update_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month,
        status: model.SimStatus,
    ):
        ...

    @abstractmethod
    def update_sim_status_by_imsi(self, imsi: IMSI, sim_status: model.SimStatus):
        ...

    @abstractmethod
    def sim_status_details(
        self,
        sim_status: model.SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> Iterator[model.SimStatusDetails]:
        ...

    @abstractmethod
    def sim_status_details_count(
        self,
        sim_status: model.SimStatus,
        account_id: int | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_provider_id(self, imsi: IMSI) -> int:
        ...

    @abstractmethod
    def copy_monthly_statistics(self) -> Iterator[model.SIMMonthlyStatus]:
        ...

    @abstractmethod
    def add_bulk_sim_monthly_statistics(
        self,
        sim_monthly_status: list[model.SIMMonthlyStatus],
    ) -> int:
        ...

    @abstractmethod
    def get_imsis(
        self,
        iccids: list[ICCID],
    ) -> Iterator[model.IMSIDetails]:
        ...

    @abstractmethod
    def get_market_share_by_account(
        self, account_id: int
    ) -> Iterator[model.MarketShareIMSI]:
        ...

    @abstractmethod
    def get_market_share(self) -> Iterator[model.MarketShareIMSI]:
        ...

    @abstractmethod
    def get_carrier_name(
        self,
        carrier_name: str | None = None,
    ) -> Iterator[model.CarrierName]:
        ...

    @abstractmethod
    def add_allocation_list(
        self,
        allocation_list: list[model.Allocation],
        allocation_details: model.AllocationDetails,
        imsi_list,
    ):
        ...

    @abstractmethod
    def update_ranges(self, range_id: int, counts: int) -> None:
        ...

    def update_allocation_id(self, range, allocation, imsi_list) -> None:
        ...

    def allocation_data(
        self, imsi_list: list[IMSI], account_id, rate_plan_id, title, created_by
    ):
        ...

    @abstractmethod
    def get_all_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.CustomModel]:
        ...

    def add_allocations_details(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        ...

    @abstractmethod
    def add_missing_monthly_data(
        self,
        imsis: IMSI,
        is_first_active: bool,
        copy_month: Month,
    ):
        ...

    @abstractmethod
    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> int | None:
        ...

    @abstractmethod
    def get_allocated_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.Allocation]:
        ...

    @abstractmethod
    def get_rate_plan_by_imsi(self, imsi: IMSI) -> int:
        ...

    @abstractmethod
    def imsi_reallocation_func(
        self,
        imsis: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> str:
        ...

    @abstractmethod
    def update_sim_msisdn_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        ...

    @abstractmethod
    def is_msisdn_already_exists(self, msisdn: MSISDN) -> bool:
        ...

    @abstractmethod
    def is_imsi_exists(self, imsi: IMSI) -> bool:
        ...

    def get_allocation_count_by_rate_plan(self, id: int) -> int:
        ...

    @abstractmethod
    def get_rate_plan_with_allocation_count(
        self, rate_plan_id: int
    ) -> model.RatePlanInfo:
        ...

    @abstractmethod
    def allocation_count_by_account(self, id: int) -> int:
        ...

    @abstractmethod
    def get_msisdn_factor(
        self, msisdn_factor: model.MSISDNFactor, free_count: int | None = None
    ) -> list[MSISDN]:
        ...

    @abstractmethod
    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        sim_profile: model.SimProfile,
        allocation_id: int,
        existing_msisdn: MSISDN,
        existing_profile: model.SimProfile,
    ) -> bool:
        ...

    @abstractmethod
    def pool_msisdn_count(self, searching: Searching | None = None) -> int:
        ...

    @abstractmethod
    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    @abstractmethod
    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    @abstractmethod
    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        ...

    @abstractmethod
    def get_msisdn_in_pool_msisdn_table(self, msisdn_list) -> list[model.MSISDN]:
        ...

    @abstractmethod
    def get_msisdn_in_sim_card_table(self, msisdn_list) -> list[model.MSISDN]:
        ...

    @abstractmethod
    def upload_msisdn(self, msdidn_list: list[model.MsisdnPool]):
        ...

    @abstractmethod
    def validate_sim_card_details_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        ...

    @abstractmethod
    def validate_bulk_sim_card_details(
        self,
        sim_card_list: list[dict],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[model.MSISDN],
    ) -> list[model.SimCardData]:
        ...

    @abstractmethod
    def bulk_update_sim_card_details(
        self,
        all_sim_card_details: list[model.SimCardData],
        sim_profile: model.SimProfile,
    ) -> bool:
        ...

    @abstractmethod
    def bulk_update_msisdn_pool(
        self,
        imsis: list[IMSI],
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        same_factor_allocation: bool,
        msisdns: list[MSISDN] | None = [],
    ) -> bool:
        ...

    @abstractmethod
    def get_invalid_sim_and_msisdn_count(self) -> model.InvalidSimMsisdnCountDetails:
        ...

    @abstractmethod
    def get_available_msisdn(
        self, imsi_list: list[IMSI], msisdn_factor: model.MSISDNFactor
    ) -> list[MSISDN]:
        ...

    @abstractmethod
    def validate_msisdn_update_request(
        self, msisdn_list: list[MSISDN], validation_key: str
    ) -> bool:
        ...

    @abstractmethod
    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        ...

    @abstractmethod
    def imsis_to_delete(self, imsis_list: list[IMSI]) -> dict[str, int]:
        ...

    @abstractmethod
    def get_sim_imsi_by_account_id(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> list[str]:
        ...

    @abstractmethod
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...


class InMemorySimRepository(AbstractSimRepository):
    def __init__(self):
        self.provider_log = []
        self.ranges: dict[int, model.Range] = {}
        self.sim_summary = {}
        self.sim_cards = {}
        self.notifications = []
        self.audit_logs_data = []
        self.sim_cdr_data = {}
        self.accounts = {}
        self.rate_plans = {}
        self.card_statistic: list[dict[str, Any]] = []
        self.sim_monthly_statuses = []
        self.next_id = 1
        self.sim_status = {}
        self.allocations = {}
        self.carrier_name_data = []
        self.sim_range_data = []
        self.sim_card_data = []
        self.allocation_list = []
        self.imsi_account = 1

    @property
    def _all_sim_cards(self) -> Iterable[model.SIMCard]:
        for range_ in self.ranges.values():
            yield from range_.sim_cards

    @property
    def _all_allocations(self) -> Iterable[model.Allocation]:
        for range_ in self.ranges.values():
            yield from range_.allocations

    def add_card_active_statistic(self, records: Iterable[dict]):
        self.card_statistic.extend(records)

    def add_sim_summary(self, sim_summary):
        self.sim_summary = sim_summary

    def add_sim_card(self, imsi: str, msisdn: str):
        self.sim_cards[imsi] = {"msisdn": msisdn}

    def add_sim_card_data(self, sim_cards):
        self.sim_cards = sim_cards

    def add_sim_status_data(self, sim_status):
        self.sim_status = sim_status

    def add_range(
        self, range_: model.Range, msisdn_pool: list[model.MsisdnPool] | None = None
    ) -> None:
        range_.id = len(self.ranges) + 1
        range_.created_at = datetime.utcnow()
        range_.allocations = []
        if hasattr(range_, "_sim_cards"):
            for i, sim_card in enumerate(range_.sim_cards):
                sim_card.id = i + 1
                sim_card.range_id = range_.id
        else:
            range_.sim_cards = []
        self.ranges[range_.id] = range_

    def get_range_list(self) -> Iterator[model.Range]:
        yield from self.ranges.values()

    def check_imsi_range(self, imsi_first: IMSI, imsi_last: IMSI) -> int | None:
        return len(
            [sim for sim in self._all_sim_cards if sim.imsi in [imsi_first, imsi_last]]
        )

    def get_range_by_id(self, range_id: int) -> model.Range | None:
        return self.ranges.get(range_id, None)

    def update_range(self, range_: model.Range) -> None:
        self.ranges[range_.id] = range_  # type: ignore
        for i, allocation in enumerate(range_.allocations):
            if not allocation.id:
                allocation.id = i + 1
            allocation.sim_cards = list(
                filter(
                    lambda sim_card: allocation.imsi_first  # type: ignore
                    <= sim_card.imsi
                    <= allocation.imsi_last,  # type: ignore
                    self._all_sim_cards,
                )
            )

    def get_allocation_by_id(self, allocation_id: int) -> model.Allocation | None:
        allocations = self.get_allocations()
        for allocation in allocations:
            if allocation.id == allocation_id:
                return allocation
        return None

    def get_last_allocation_in_range(self, range_id: int) -> model.Allocation | None:
        try:
            range_allocations = filter(
                lambda allocation: allocation.range_id == range_id,
                self.get_allocations(),
            )
            return max(range_allocations, key=attrgetter("imsi_last"))
        except ValueError:
            return None

    def get_allocations(self) -> list[model.Allocation]:
        return list(self._all_allocations)

    def get_simcards(self) -> list[model.SIMCard]:
        return list(self._all_sim_cards)

    def add_allocation(self, allocation: model.Allocation) -> None:
        allocation.id = len(self.get_allocations()) + 1
        allocation.created_at = datetime.utcnow()
        if not allocation.imsi_last or not allocation.imsi_first:  # type: ignore
            raise AssertionError(
                "The allocation fields must be already calculated from the range"
            )
        allocation.sim_cards = list(
            filter(
                lambda sim_card: allocation.imsi_first  # type: ignore
                <= sim_card.imsi
                <= allocation.imsi_last,  # type: ignore
                self._all_sim_cards,
            )
        )
        range_ = self.get_range_by_id(allocation.range_id)
        if range_ is None:
            raise AssertionError(f"The range with id {allocation.range_id} must exist.")
        range_.allocations.append(allocation)

    def update_sim_cards_with_allocation(self, allocation: model.Allocation) -> None:
        imsi_first = allocation.imsi_first  # type: ignore
        imsi_last = allocation.imsi_last  # type: ignore
        if imsi_first and imsi_last:
            for sim_card in [
                sc for sc in self._all_sim_cards if imsi_first <= sc.imsi <= imsi_last
            ]:
                sim_card.allocation_id = allocation.id
                sim_card.allocation = allocation

    def remove_allocations_in_range(self, range: model.Range) -> None:
        for allocation in list(range.allocations):
            self.remove_allocation(allocation)

    def remove_allocation(self, allocation: model.Allocation) -> None:
        range: model.Range = self.get_range_by_id(allocation.range_id)  # type: ignore
        for index, allocation_ in enumerate(range.allocations):
            if allocation_.id == allocation.id:
                range.remaining += allocation.quantity  # type: ignore
                for sim_card in allocation.sim_cards:
                    sim_card.rate_plan_id = None
                range.allocations.pop(index)

    def remove_range(self, range: model.Range) -> None:
        if range.id is None:
            raise AssertionError("Range must have an id")
        self.ranges.pop(range.id)

    def get_sim_cards(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: model.SimStatus | None = None,
    ) -> Iterator[model.SIMCard]:
        def filter_by_account_id(sim_card: model.SIMCard) -> bool:
            if account_id is None:  # disabled filter
                return True
            if (
                sim_card.allocation is None
            ):  # filter enabled but sim card has no allocation and account
                return False
            else:
                return sim_card.allocation.account_id == account_id

        def populate_allocation_rate_plan(sim_card: model.SIMCard):
            if sim_card.allocation is not None and sim_card.rate_plan_id is None:
                sim_card.rate_plan_id = sim_card.allocation.rate_plan_id

            return sim_card

        def filter_by_rate_plan_ids(sim_card: model.SIMCard) -> bool:
            if rate_plan_ids is None:
                return True
            return sim_card.rate_plan_id in rate_plan_ids

        def filter_by_imsi_list(sim_card: model.SIMCard) -> bool:
            if imsi_list is None:
                return True
            return sim_card.imsi in imsi_list

        def filter_by_creation_month(sim_card: model.SIMCard) -> bool:
            if creation_month is None:
                return True
            elif sim_card.allocation is None or sim_card.allocation.created_at is None:
                return False
            return (
                Month.from_date(sim_card.allocation.created_at.date().replace(day=1))
                == creation_month
            )

        def filter_by_search(sim_card: model.SIMCard) -> bool:
            if searching is None:
                return True
            return searching.filter(sim_card)

        def populate_form_factor(sim_card: model.SIMCard):
            if sim_card.form_factor is None:
                if sim_card.range_id is not None:
                    range_ = self.get_range_by_id(sim_card.range_id)
                    if range_ is not None:
                        sim_card.form_factor = range_.form_factor
            return sim_card

        filtered_by_account = filter(filter_by_account_id, self._all_sim_cards)
        filtered_by_search = filter(filter_by_search, filtered_by_account)
        filtered_by_imsi_list = filter(filter_by_imsi_list, filtered_by_search)
        filtered_by_creation_month = filter(
            filter_by_creation_month, filtered_by_imsi_list
        )
        with_rate_plan = map(populate_allocation_rate_plan, filtered_by_creation_month)
        filtered_by_rate_plan_id = filter(filter_by_rate_plan_ids, with_rate_plan)
        final_records = list(map(populate_form_factor, filtered_by_rate_plan_id))

        if ordering:
            mapped_fields = {
                "allocation_reference": attrgetter("allocation.title"),
                "allocation_date": attrgetter("allocation.created_at"),
            }
            order_resolver = OrderingResolver(
                mapped_fields=mapped_fields, ordering=ordering
            )
            final_records = order_resolver.sort_records(final_records)

        if pagination:
            yield from pagination.slice(final_records)
        else:
            yield from final_records

    def get_sim_count(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: model.SimStatus | None = None,
    ) -> int:
        return ilen(
            self.get_sim_cards(
                account_id,
                rate_plan_ids,
                creation_month=creation_month,
                imsi_list=imsi_list,
                searching=searching,
                active_only=active_only,
                deactivated_only=deactivated_only,
                pending_only=pending_only,
                ready_activation_only=ready_activation_only,
                sim_status=sim_status,
            )
        )

    def get_sim_remains(self) -> Iterator[tuple[FormFactor, int]]:
        for ffactor in FormFactor:
            yield ffactor, sum(
                range_.remaining
                for range_ in self.get_range_list()
                if range_.form_factor == ffactor
            )

    def update_sim_card(self, imsi: IMSI, sim_status: model.SimStatus) -> None:
        self.sim_cards[imsi].sim_status = sim_status.name

    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        sim_data = self.sim_summary.get(imsi)
        if sim_data is None:
            raise IMSIDoesNotExit(imsi)
        connection_history_summary = model.ConnectionSummary(
            msisdn=sim_data["msisdn"],
            imsi=sim_data["imsi"],
            iccid=sim_data["iccid"],
            first_activated=None,
            last_session=None,
            sim_status=sim_data["sim_status"],
            rate_plan_id=sim_data["rate_plan_id"],
            rate_plan=sim_data["rate_plan"],
        )
        return connection_history_summary

    def get_sim_usage(
        self, account_id=1, ordering=None, searching=None, pagination=None
    ) -> Iterator[model.SimUsage]:
        for sim_management in self.sim_cards:
            if sim_management["account_id"] == account_id:
                yield model.SimUsage(**sim_management)

    def get_sim_usage_count(
        self, account_id: int | None = None, searching: Searching | None = None
    ) -> tuple[int, int, int, int, int]:
        return (
            10,
            2,
            2,
            2,
            4,
        )

    def cards_active_statistic(
        self, account_id: int, month: Month, pagination: Pagination | None = None
    ) -> Iterator[model.ActiveSimMonthlyStatistic]:
        for row in self.card_statistic:
            if row.get("account_id") == account_id and row.get("month") == month:
                yield model.ActiveSimMonthlyStatistic(**row)

    def cards_active_statistic_count(self, account_id: int, month: Month) -> int:
        return ilen(
            sim
            for sim in self.cards_active_statistic(account_id=account_id, month=month)
        )

    def add_sim_monthly_status(
        self,
        sim_monthly_status,
    ) -> int:
        sim_monthly_status["sim_card_id"] = self.next_id
        self.sim_monthly_statuses.append(sim_monthly_status)
        self.next_id += 1
        return sim_monthly_status["sim_card_id"]

    def check_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month | None,
    ) -> bool:
        for sim_monthly_status in self.sim_monthly_statuses:
            if (
                sim_monthly_status["sim_card_id"] == sim_card_id
                and sim_monthly_status["month"] == month
            ):
                return True
        return False

    def update_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month,
        status: model.SimStatus,
    ):
        for sim_monthly_status in self.sim_monthly_statuses:
            if (
                sim_monthly_status["sim_card_id"] == sim_card_id
                and sim_monthly_status["month"] == month
            ):
                sim_monthly_status["sim_status"] = status

    def add_sim_cards(self, imsi: str, msisdn: str, status: model.SimStatus):
        self.sim_cards["imsi"] = imsi
        self.sim_cards["msisdn"] = msisdn
        self.sim_cards["sim_status"] = status

    def update_sim_status_by_imsi(self, imsi: IMSI, sim_status: model.SimStatus):
        if self.sim_cards["imsi"] == imsi:
            self.sim_cards["sim_status"] = sim_status

    def sim_status_details(self):
        statistics_sim_data = self.sim_status
        sim_status = model.SimStatusDetails(
            imsi=statistics_sim_data["imsi"],
            iccid=statistics_sim_data["iccid"],
            msisdn=statistics_sim_data["msisdn"],
            sim_status=statistics_sim_data["sim_status"],
        )
        return sim_status

    def sim_status_details_count(
        self,
        sim_status: model.SimStatus,
        account_id: int | None = None,
    ) -> int:
        return ilen(sim_detail for sim_detail in self.sim_status_details())

    def get_provider_id(self, imsi: IMSI) -> int:
        for entry in self.provider_log:
            if entry.imsi == imsi:
                return entry.id
        return entry.id

    def copy_monthly_statistics(self) -> Iterator[model.SIMMonthlyStatus]:
        today = datetime.today()
        year = today.year
        month = today.month
        current_month = Month(year=year, month=month, day=1)
        self.sim_monthly_statuses.append(
            {
                "sim_card_id": 1,
                "month": current_month,
                "sim_status": model.SimStatus.ACTIVE,
                "is_first_activation": True,
            }
        )
        for monthly_data in self.sim_monthly_statuses:
            if monthly_data["month"] == current_month:
                yield model.SIMMonthlyStatus(**monthly_data)

    def add_bulk_sim_monthly_statistics(
        self,
        sim_monthly_status: list[model.SIMMonthlyStatus],
    ) -> int:
        self.sim_monthly_statuses = sim_monthly_status
        return len(sim_monthly_status)

    def get_imsis(self, iccids: list[ICCID]) -> Iterator[model.IMSIDetails]:
        return self.sim_cards.get(iccids)

    def get_market_share_by_account(
        self, account_id: int
    ) -> Iterator[model.MarketShareIMSI]:
        return self.sim_cards.get(account_id)

    def get_market_share(self) -> Iterator[model.MarketShareIMSI]:
        return self.sim_cards.get()

    def get_carrier_name(
        self, carrier_name: str | None = None
    ) -> Iterator[model.CarrierName]:
        return self.carrier_name_data.get(carrier_name, None)

    def add_allocation_list(
        self,
        allocation_list: list[model.Allocation],
        allocation_details: model.AllocationDetails,
        imsi_list,
    ) -> dict:
        for allocation in allocation_list:
            self.allocation_list.append(allocation)

        range_to_allocation_dict = {
            allocation.range_id: allocation.id for allocation in allocation_list
        }
        return range_to_allocation_dict

    def update_ranges(self, range_id: int, counts: int) -> None:
        for range_obj in self.sim_range_data:
            if range_obj.id == range_id:
                range_obj.remaining -= counts

    def get_all_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.CustomModel]:
        imsi_list = []
        for sim_range in self.sim_range_data:
            for sim_card in self.sim_card_data:
                if (
                    sim_card["range_id"] == sim_range["id"]
                    and sim_card["allocation_id"] is None
                    and sim_card["imsi"] in imsi_list
                ):
                    imsi_list.append(sim_card["imsi"])

        for imsi in imsi_list:
            yield model.CustomModel(
                imsi=imsi, allocation_id=None, form_factor=FormFactor.STANDARD
            )

    def add_missing_monthly_data(
        self,
        imsis: IMSI,
        is_first_active: bool,
        copy_month: Month,
    ):
        ...

    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> int | None:
        if imsi == ["***************"]:
            return self.imsi_account
        else:
            return None

    def imsi_reallocation_func(
        self,
        imsis: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> str:
        ...

    def get_allocated_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.Allocation]:
        ...

    def get_rate_plan_by_imsi(self, imsi: IMSI) -> int:
        ...

    def is_msisdn_already_exists(self, msisdn: MSISDN) -> bool:
        ...

    def update_sim_msisdn_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        ...

    def is_imsi_exists(self, imsi: IMSI) -> bool:
        ...

    def get_allocation_count_by_rate_plan(self, id: int) -> int:
        return 5

    def get_rate_plan_with_allocation_count(
        self, rate_plan_id: int
    ) -> model.RatePlanInfo:
        ...

    def allocation_count_by_account(self, id: int) -> int:
        return 9

    def get_msisdn_factor(
        self, msisdn_factor: model.MSISDNFactor, free_count: int | None = None
    ) -> list[MSISDN]:
        ...

    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        sim_profile: model.SimProfile,
        allocation_id: int,
        existing_msisdn: MSISDN,
        existing_profile: model.SimProfile,
    ) -> bool:
        ...

    def pool_msisdn_count(self, searching: Searching | None = None) -> int:
        ...

    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        ...

    def get_msisdn_in_pool_msisdn_table(self, msisdn_list) -> list[model.MSISDN]:
        return []

    def get_msisdn_in_sim_card_table(self, msisdn_list) -> list[model.MSISDN]:
        return []

    def upload_msisdn(self, msdidn_list: list[model.MsisdnPool]) -> None:
        ...

    def validate_sim_card_details_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        ...

    def validate_bulk_sim_card_details(
        self,
        sim_card_list: list[dict],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[model.MSISDN],
    ) -> list[model.SimCardData]:
        ...

    def bulk_update_sim_card_details(
        self,
        all_sim_card_details: list[model.SimCardData],
        sim_profile: model.SimProfile,
    ) -> bool:
        ...

    def bulk_update_msisdn_pool(
        self,
        imsis: list[IMSI],
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        same_factor_allocation: bool,
        msisdns: list[MSISDN] | None = [],
    ) -> bool:
        ...

    def get_invalid_sim_and_msisdn_count(self) -> model.InvalidSimMsisdnCountDetails:
        ...

    def get_available_msisdn(
        self, imsi_list: list[IMSI], msisdn_factor: model.MSISDNFactor
    ) -> list[MSISDN]:
        ...

    def validate_msisdn_update_request(
        self, msisdn_list: list[MSISDN], validation_key: str
    ) -> bool:
        ...

    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        ...

    def imsis_to_delete(self, imsis_list: list[IMSI]) -> dict[str, int]:
        ...

    def get_sim_imsi_by_account_id(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> list[str]:
        ...

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...


class DatabaseSimRepository(AbstractSimRepository):
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def add_range(
        self, range_: model.Range, msisdn_pool: list[model.MsisdnPool] | None = None
    ) -> None:
        if msisdn_pool is not None:
            self.session.add_all(msisdn_pool)
        self.session.add(range_)
        self.session.commit()

    def get_range_list(self) -> Iterator[model.Range]:
        query = select(orm.sim_range).order_by(orm.sim_range.c.created_at.desc())
        for row in self.session.execute(query).mappings():
            yield model.Range(**row)

    def check_imsi_range(self, imsi_first: IMSI, imsi_last: IMSI) -> int | None:
        table = orm.sim_card
        query = select(table).filter(
            or_(table.c.imsi == imsi_first, table.c.imsi == imsi_last)
        )
        return self.session.execute(query).scalar()

    def get_range_by_id(self, range_id: int) -> model.Range | None:
        query = select(model.Range).filter(model.Range.id == range_id)
        return self.session.execute(query).scalar_one_or_none()

    def update_range(self, range_: model.Range) -> None:
        self.session.merge(range_)
        self.session.commit()

    def get_allocation_by_id(self, allocation_id) -> model.Allocation | None:
        return self.session.query(model.Allocation).filter_by(id=allocation_id).first()

    def get_last_allocation_in_range(self, range_id: int) -> model.Allocation | None:
        return (
            self.session.query(model.Allocation)
            .filter_by(range_id=range_id)
            .order_by(desc(model.Allocation.imsi_last))  # type: ignore
            .first()
        )

    def add_allocation(self, allocation: model.Allocation) -> None:
        self.session.add(allocation)
        self.session.commit()

    def update_sim_cards_with_allocation(self, allocation: model.Allocation) -> None:
        table = orm.sim_card
        self.session.query(table).filter(
            and_(
                table.c.imsi >= allocation.imsi_first,  # type: ignore
                table.c.imsi <= allocation.imsi_last,  # type: ignore
            )
        ).update({table.c.allocation_id: allocation.id})
        self.session.commit()

    def remove_allocations_in_range(self, range: model.Range) -> None:
        for allocation in range.allocations:
            self.session.delete(allocation)
        self.session.commit()

    def remove_allocation(self, allocation: model.Allocation) -> None:
        self.session.delete(allocation)
        self.session.commit()

    def remove_range(self, range_: model.Range) -> None:
        try:
            self.session.delete(range_)
            self.session.commit()
        except IntegrityError as e:
            logger.error(f"Error:{e}")
            raise RangeIntegrityError(("Range could not be deleted."))

    def get_sim_cards(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        source = model.SIMCard
        rate_plan_id_col = func.coalesce(
            model.SIMCard.rate_plan_id, model.Allocation.rate_plan_id
        )
        msisdn_pool = model.MsisdnPool
        query = (
            select(
                source.id,
                source.range_id,
                source.iccid,
                source.imsi,
                source.msisdn,
                source.allocation_id,
                source.form_factor,
                source.sim_status,
                func.coalesce(
                    model.SIMCard.rate_plan_id, model.Allocation.rate_plan_id
                ).label("rate_plan_id"),
                msisdn_pool.sim_profile,
            )
            .select_from(source)
            .join(
                msisdn_pool,
                msisdn_pool.msisdn == source.msisdn,
            )
            .join(model.Allocation, isouter=True)
        )
        if account_id is not None:
            query = query.filter(model.Allocation.account_id == account_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.SIMCard, query)
        if rate_plan_ids is not None:
            query = query.filter(rate_plan_id_col.in_(rate_plan_ids))
        if imsi_list is not None:
            query = query.filter(source.imsi.in_(imsi_list))  # type: ignore
        if creation_month is not None:
            query = query.filter(
                and_(
                    model.Allocation.created_at >= creation_month,  # type: ignore
                    model.Allocation.created_at < creation_month.next(),  # type: ignore
                )
            )
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )

            fields_mapper = {
                "allocation_reference": model.Allocation.title,
                "allocation_date": model.Allocation.created_at,
            }
            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(source, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(source.id)
            )
        for row in self.session.execute(query).mappings():
            yield model.SIMCard(**row)

    def get_sim_count(
        self,
        account_id: int | None = None,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: model.SimStatus | None = None,
    ) -> int:
        sim = orm.sim_card
        allocation = orm.sim_allocation
        query = select(count(sim.c.id)).outerjoin(allocation)
        if active_only:
            query = query.where(sim.c.sim_status == model.SimStatus.ACTIVE.name)
        if deactivated_only:
            query = query.where(sim.c.sim_status == model.SimStatus.DEACTIVATED.name)
        if pending_only:
            query = query.where(sim.c.sim_status == model.SimStatus.PENDING.name)
        if ready_activation_only:
            query = query.where(
                sim.c.sim_status == model.SimStatus.READY_FOR_ACTIVATION.name
            )
        if account_id is not None:
            query = query.filter_by(account_id=account_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.SIMCard, query)
        if rate_plan_ids is not None:
            # New Billing logic while to calculate sim count
            query = query.filter(
                func.coalesce(
                    sim.c.rate_plan_id.in_(rate_plan_ids),
                    allocation.c.rate_plan_id.in_(rate_plan_ids),
                )
            )
            # New Billing logic while to calculate sim count
        if creation_month is not None:
            query = query.filter(
                and_(
                    allocation.c.created_at >= creation_month,
                    allocation.c.created_at < creation_month.next(),
                )
            )
        if imsi_list is not None:
            query = query.filter(sim.c.imsi.in_(imsi_list))
        if sim_status is not None:
            query = query.filter(sim.c.sim_status == sim_status)

        return self.session.execute(query).scalar_one()

    def get_sim_remains(self) -> Iterator[tuple[FormFactor, int]]:
        range_ = orm.sim_range
        query = select(
            func.sum(range_.c.remaining).label("remaining"), range_.c.form_factor
        ).group_by(range_.c.form_factor)
        for remain_data in self.session.execute(query):
            yield remain_data.form_factor, remain_data.remaining

    def update_sim_card(self, imsi: IMSI, sim_status: model.SimStatus) -> None:
        sim_card = model.SIMCard
        self.session.query(sim_card).filter(sim_card.imsi == imsi).update(
            {sim_card.sim_status: sim_status.name}
        )
        self.session.commit()

    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        sim = orm.sim_card
        sim_first_activated = orm.active_sim_monthly_statistic
        allocation = orm.sim_allocation
        query = (
            select(
                sim.c.id,
                sim.c.msisdn,
                sim.c.imsi,
                sim.c.iccid,
                sim.c.sim_status,
                func.coalesce(allocation.c.rate_plan_id, sim.c.rate_plan_id).label(
                    "rate_plan_id"
                ),
            )
            .join(allocation, allocation.c.id == sim.c.allocation_id)
            .filter(sim.c.imsi == imsi)
        )
        sim_details = self.session.execute(query).first()
        if sim_details is None:
            raise IMSIDoesNotExit(imsi)

        stmt = select(func.min(sim_first_activated.c.month)).filter(
            sim_first_activated.c.sim_card_id == sim_details.id,
            sim_first_activated.c.is_first_activation == self.condition_true,
        )
        first_activated_at = self.session.execute(stmt).scalar_one()
        connection_history_summary = model.ConnectionSummary(
            msisdn=sim_details.msisdn,
            imsi=sim_details.imsi,
            iccid=sim_details.iccid,
            first_activated=first_activated_at,
            last_session=None,
            sim_status=sim_details.sim_status,
            rate_plan_id=sim_details.rate_plan_id,
            rate_plan=None,
        )
        return connection_history_summary

    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterator[model.SimUsage]:
        sim = orm.sim_card
        range = orm.sim_range
        allocation = orm.sim_allocation
        msisdn_pool = orm.msisdn_pool
        # TODO exception join for sim rate plan
        rate_plan = rate_plan_orm.RatePlan
        rate_plan_sim = aliased(rate_plan)
        rate_plan_allocation = aliased(rate_plan)
        query = (
            select(
                sim.c.id.label("sim_id"),
                sim.c.iccid,
                sim.c.msisdn,
                sim.c.imsi,
                range.c.form_factor.label("type"),
                allocation.c.title.label("allocation_reference"),
                allocation.c.created_at.label("allocation_date"),
                sim.c.sim_status,
                literal(0).label("usage"),
                literal(0).label("ee_usage"),
                func.coalesce(rate_plan_sim.name, rate_plan_allocation.name).label(
                    "rate_plan"
                ),
                msisdn_pool.c.sim_profile,
                msisdn_pool.c.msisdn_factor,
            )
            .join(range, range.c.id == sim.c.range_id)
            .join(
                allocation,
                and_(
                    allocation.c.id == sim.c.allocation_id,
                    allocation.c.range_id == range.c.id,
                ),
            )
            .join(
                msisdn_pool,
                and_(
                    msisdn_pool.c.msisdn == sim.c.msisdn,
                ),
            )
            .outerjoin(rate_plan_sim, rate_plan_sim.id == sim.c.rate_plan_id)
            .outerjoin(
                rate_plan_allocation,
                rate_plan_allocation.id == allocation.c.rate_plan_id,
            )
        )
        if account_id is not None:
            query = query.filter(allocation.c.account_id == account_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.SIMCard, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )
            fields_mapper = {
                "type": model.Range.form_factor,
                "allocationReference": model.Allocation.title,
                "allocationDate": model.Allocation.created_at,
                "simStatus": model.SIMCard.sim_status,
                "ratePlan": rate_plan_sim.name,
            }
            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(model.SIMCard, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(sim.c.iccid)
            )

        for row in self.session.execute(query).mappings():
            yield model.SimUsage(**row)

    def get_sim_imsi_by_account_id(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> list[str]:
        sim = orm.sim_card
        range = orm.sim_range
        allocation = orm.sim_allocation
        msisdn_pool = orm.msisdn_pool
        # TODO exception join for sim rate plan
        rate_plan = rate_plan_orm.RatePlan
        rate_plan_sim = aliased(rate_plan)
        rate_plan_allocation = aliased(rate_plan)
        query = (
            select(func.array_agg(sim.c.imsi).label("imsi"))
            .join(range, range.c.id == sim.c.range_id)
            .join(
                allocation,
                and_(
                    allocation.c.id == sim.c.allocation_id,
                    allocation.c.range_id == range.c.id,
                ),
            )
            .join(
                msisdn_pool,
                and_(
                    msisdn_pool.c.msisdn == sim.c.msisdn,
                ),
            )
            .outerjoin(rate_plan_sim, rate_plan_sim.id == sim.c.rate_plan_id)
            .outerjoin(
                rate_plan_allocation,
                rate_plan_allocation.id == allocation.c.rate_plan_id,
            )
        )
        if account_id is not None:
            query = query.filter(allocation.c.account_id == account_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.SIMCard, query)

        imsi_list = self.session.execute(query).scalar()
        if imsi_list is None or imsi_list == []:
            raise NoSimFound()

        return imsi_list

    def get_sim_usage_count(
        self, account_id: int | None = None, searching: Searching | None = None
    ) -> tuple[int, int, int, int, int]:
        sim = orm.sim_card
        range = orm.sim_range
        allocation = orm.sim_allocation
        msisdn_pool = orm.msisdn_pool
        query = (
            select(
                count(sim.c.id),
                count(case((sim.c.sim_status == SimStatus.ACTIVE, 1))).label(
                    "active_sim_count"
                ),
                count(case((sim.c.sim_status == SimStatus.DEACTIVATED, 1))).label(
                    "deactive_sim_count"
                ),
                count(case((sim.c.sim_status == SimStatus.PENDING, 1))).label(
                    "pending_sim_count"
                ),
                count(
                    case((sim.c.sim_status == SimStatus.READY_FOR_ACTIVATION, 1))
                ).label("ready_for_activation_sim_count"),
            )
            .join(range, range.c.id == sim.c.range_id)
            .join(
                allocation,
                and_(
                    allocation.c.id == sim.c.allocation_id,
                    allocation.c.range_id == range.c.id,
                ),
            )
            .join(
                msisdn_pool,
                and_(
                    msisdn_pool.c.msisdn == sim.c.msisdn,
                ),
            )
        )
        if account_id is not None:
            query = query.filter(allocation.c.account_id == account_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.SIMCard, query)
        (
            total,
            active_sim_count,
            deactive_sim_count,
            pending_sim_count,
            ready_for_activation_sim_count,
        ) = self.session.execute(query).one()
        return (
            total,
            active_sim_count,
            deactive_sim_count,
            pending_sim_count,
            ready_for_activation_sim_count,
        )

    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> Iterator[model.ActiveSimMonthlyStatistic]:
        sim_monthly_statistic = orm.active_sim_monthly_statistic
        sim_card = orm.sim_card
        sim_allocation = orm.sim_allocation
        rate_plan_id = func.coalesce(
            sim_card.c.rate_plan_id,
            sim_allocation.c.rate_plan_id,
        ).label("rate_plan_id")
        query = (
            select(
                sim_card.c.id,
                sim_card.c.imsi,
                sim_card.c.iccid,
                sim_card.c.msisdn,
                rate_plan_id,
                sim_monthly_statistic.c.sim_status,
                sim_monthly_statistic.c.is_first_activation,
                literal(0).label("usage"),
                # New Billing logic for audit purpose
                sim_card.c.allocation_id,
                sim_allocation.c.created_at,
                # New Billing logic for audit purpose
            )
            .join(
                sim_monthly_statistic,
                sim_monthly_statistic.c.sim_card_id == sim_card.c.id,
            )
            .join(sim_allocation, sim_allocation.c.id == sim_card.c.allocation_id)
            .filter(
                sim_allocation.c.account_id == account_id,
                sim_monthly_statistic.c.month == month,
            )
        )
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        for row in self.session.execute(query).mappings():
            yield model.ActiveSimMonthlyStatistic(**row)

    def cards_active_statistic_count(self, account_id: int, month: Month) -> int:
        sim_monthly_statistic = orm.active_sim_monthly_statistic
        sim_card = orm.sim_card
        sim_allocation = orm.sim_allocation

        query = (
            select(count(sim_card.c.id))
            .join(
                sim_monthly_statistic,
                sim_monthly_statistic.c.sim_card_id == sim_card.c.id,
            )
            .join(sim_allocation, sim_allocation.c.id == sim_card.c.allocation_id)
            .filter(
                sim_allocation.c.account_id == account_id,
                sim_monthly_statistic.c.month == month,
            )
        )
        return self.session.execute(query).scalar_one()

    def add_sim_monthly_status(
        self,
        sim_monthly_status: model.SIMMonthlyStatus,
    ) -> int:
        self.session.add(sim_monthly_status)
        self.session.commit()
        return sim_monthly_status.sim_card_id

    def check_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month | None,
    ) -> bool:
        sim_monthly_status = orm.active_sim_monthly_statistic
        query = select(sim_monthly_status.c.id).filter(
            sim_monthly_status.c.sim_card_id == sim_card_id
        )
        if month is not None:
            query = query.filter(sim_monthly_status.c.month == month)

        sim_monthly_status_data = self.session.execute(query).first()

        if not sim_monthly_status_data:
            return False
        return True

    def update_sim_monthly_status(
        self,
        sim_card_id: int,
        month: Month,
        status: model.SimStatus,
    ):
        sim_monthly_status = orm.active_sim_monthly_statistic
        self.session.query(sim_monthly_status).filter(
            and_(
                sim_monthly_status.c.sim_card_id == sim_card_id,
                sim_monthly_status.c.month == month,
            )
        ).update({sim_monthly_status.c.sim_status: status.name})
        self.session.commit()

    def update_sim_status_by_imsi(self, imsi: IMSI, sim_status: model.SimStatus):
        sim_card = model.SIMCard
        self.session.query(sim_card).filter(sim_card.imsi == imsi).update(
            {sim_card.sim_status: sim_status}
        )
        self.session.commit()

    def update_sim_msisdn_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        sim_card = model.SIMCard
        self.session.query(sim_card).filter(sim_card.imsi == IMSI(imsi)).update(
            {sim_card.msisdn: MSISDN(msisdn)}
        )
        self.session.commit()

    def sim_status_details(
        self,
        sim_status: model.SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> Iterator[model.SimStatusDetails]:
        sim_card = orm.sim_card
        sim_allocation = orm.sim_allocation
        query = (
            select(
                sim_card.c.imsi,
                sim_card.c.iccid,
                sim_card.c.msisdn,
                sim_card.c.sim_status,
            )
            .join(sim_allocation, sim_allocation.c.id == sim_card.c.allocation_id)
            .filter(sim_card.c.sim_status == sim_status)
        )
        if account_id is not None:
            query = query.filter(sim_allocation.c.account_id == account_id)
        logger.debug(
            f"/cards/{{sim_status}} query with account_id {account_id} : {query}"
        )
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        logger.debug(
            f"/cards/{{sim_status}} query with pagination {pagination} : {query}"
        )
        for row in self.session.execute(query).mappings():
            yield model.SimStatusDetails(**row)

    def sim_status_details_count(
        self,
        sim_status: model.SimStatus,
        account_id: int | None = None,
    ) -> int:
        sim_card = orm.sim_card
        sim_allocation = orm.sim_allocation
        query = (
            select(count(sim_card.c.id))
            .join(sim_allocation, sim_allocation.c.id == sim_card.c.allocation_id)
            .filter(sim_card.c.sim_status == sim_status)
        )
        if account_id is not None:
            query = query.filter(sim_allocation.c.account_id == account_id)
        return self.session.execute(query).scalar_one()

    def get_provider_id(self, imsi: IMSI) -> int:
        sim_card = orm.sim_card
        query = select(sim_card.c.imsi).filter(
            sim_card.c.imsi == imsi,
            sim_card.c.sim_status == model.SimStatus.PENDING.name,
        )
        result = self.session.execute(query).first()
        return result[0] if result else 0

    def copy_monthly_statistics(self) -> Iterator[model.SIMMonthlyStatus]:
        SimMonthlyStatistic = model.SIMMonthlyStatus
        _SimMonthlyStatistic = aliased(SimMonthlyStatistic, name="_SimMonthlyStatistic")
        today = datetime.today()
        year = today.year
        month = today.month
        current_month = Month(year=year, month=month, day=1)
        prev_month = Month(year=year - (month == 1), month=(month - 2) % 12 + 1, day=1)

        query = self.session.query(
            SimMonthlyStatistic.sim_card_id,
            literal(current_month).label("month"),
            SimMonthlyStatistic.sim_status,
            literal(False).label("is_first_activation"),
        ).filter(
            SimMonthlyStatistic.month == prev_month,
            # New Billing logic if SIM deactivated then
            # not required to copy for the next month
            SimMonthlyStatistic.sim_status != SimStatus.DEACTIVATED,
            # New Billing logic if SIM deactivated then
            # not required to copy for the next month
            ~exists().where(
                and_(
                    SimMonthlyStatistic.sim_card_id == _SimMonthlyStatistic.sim_card_id,
                    _SimMonthlyStatistic.month == current_month,
                )
            ),
        )
        for row in self.session.execute(query).mappings():
            yield model.SIMMonthlyStatus(**row)

    def add_bulk_sim_monthly_statistics(
        self,
        sim_monthly_status: list[model.SIMMonthlyStatus],
    ) -> int:
        self.session.bulk_save_objects(sim_monthly_status)
        self.session.commit()
        return len(sim_monthly_status)

    def get_imsis(self, iccids: list[ICCID]) -> Iterator[model.IMSIDetails]:
        source = model.SIMCard
        query = select(
            source.iccid,
            source.imsi,
        )
        query = query.filter(source.iccid.in_(iccids))  # type: ignore
        for row in self.session.execute(query).mappings():
            yield model.IMSIDetails(**row)

    def get_market_share_by_account(
        self, account_id
    ) -> Iterator[model.MarketShareIMSI]:
        allocations = orm.sim_allocation
        sim_card = orm.sim_card
        query = (
            select(sim_card.c.imsi)
            .join(allocations, allocations.c.id == sim_card.c.allocation_id)
            .filter(allocations.c.account_id == account_id)
        )
        for row in self.session.execute(query).mappings():
            yield model.MarketShareIMSI(**row)

    def get_market_share(self) -> Iterator[model.MarketShareIMSI]:
        sim_card = orm.sim_card
        query = select(sim_card.c.imsi).where(sim_card.c.allocation_id.isnot(None))
        for row in self.session.execute(query).mappings():
            yield model.MarketShareIMSI(**row)

    def get_carrier_name(
        self, carrier_name: str | None = None
    ) -> Iterator[model.CarrierName]:
        carrier_name_data = cdrdata_orm.carrier_name
        if carrier_name:
            query = select(carrier_name_data).filter(
                carrier_name_data.c.carrier_name == carrier_name
            )
        else:
            query = select(carrier_name_data)
        for row in self.session.execute(query).mappings():
            yield model.CarrierName(**row)

    def add_allocation_list(
        self,
        allocation_list: list[model.Allocation],
        allocation_details: model.AllocationDetails,
        imsi_list,
    ) -> None:
        self.session.add(allocation_details)
        self.session.add_all(allocation_list)
        self.session.flush()
        sim_card_table = orm.sim_card
        allocation_table = orm.sim_allocation
        self.session.query(sim_card_table).filter(
            and_(
                sim_card_table.c.imsi == allocation_table.c.imsi,
                sim_card_table.c.range_id == allocation_table.c.range_id,
            )
        ).update({sim_card_table.c.allocation_id: allocation_table.c.id})
        self.session.query(allocation_table).filter(
            (allocation_table.c.imsi.in_(imsi_list))
        ).update({allocation_table.c.allocation_details_id: allocation_details.id})

        # New Billing logic while allocation we need to add this
        copy_sim_statistic_function = func.insert_monthly_statistic(imsi_list)
        query = select("*").select_from(copy_sim_statistic_function)
        self.session.execute(query)
        # New Billing logic while allocation we need to add this
        self.session.commit()

    def update_ranges(self, range_id: int, counts: int) -> None:
        remaining = {model.Range.remaining: model.Range.remaining - counts}
        self.session.query(model.Range).filter(model.Range.id == range_id).update(
            remaining
        )
        self.session.commit()

    def allocation_data(
        self, imsi_list: list[IMSI], account_id, rate_plan_id, title, created_by
    ) -> Iterator[model.Allocation]:
        range = orm.sim_range
        sim_card = orm.sim_card
        query = (
            select(
                range.c.id.label("range_id"),
                sim_card.c.imsi,
                literal(account_id).label("account_id"),
                literal(rate_plan_id).label("rate_plan_id"),
                literal(title).label("title"),
                literal(created_by).label("created_by"),
            )
            .join(
                range,
                range.c.id == sim_card.c.range_id,
            )
            .where(
                and_(
                    sim_card.c.imsi.in_(imsi_list),
                )
            )
        )
        for row in self.session.execute(query).mappings():
            yield model.Allocation(**row)

    def get_all_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.CustomModel]:
        validate_sim = func.validate_sim_card_details(imsi_list)
        query = select("*").select_from(validate_sim)
        for row in self.session.execute(query).mappings():
            yield model.CustomModel(**row)

    def add_allocations_details(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        sim_info = func.allocation_info()
        count_query = select(func.count()).select_from(sim_info)
        total_count = self.session.execute(count_query).scalar() or 0
        query = select("*").select_from(sim_info)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        result = self.session.execute(query).all()

        allocation_summaries = [
            model.AllocationSummary(
                id=row.id,
                title=row.title,
                account_id=row.account_id,
                account_name=row.name,
                quantity=row.quantity,
                created_at=row.created_at,
                form_factor=row.form_factor,
                provider=row.provider,
                logo_key=row.logo_key,
                country=row.country,
            )
            for row in result
        ]
        return allocation_summaries, total_count

    def add_missing_monthly_data(
        self,
        imsi: IMSI,
        is_first_active: bool,
        copy_month: Month,
    ):
        sim_card = orm.sim_card
        sm_statistic = orm.active_sim_monthly_statistic

        query = (
            self.session.query(
                sim_card.c.id,
                literal(copy_month).label("month"),
                sim_card.c.sim_status,
                literal(is_first_active).label("is_first_activation"),
            )
            .filter(sim_card.c.imsi == str(imsi))
            .where(
                ~exists().where(
                    and_(
                        sm_statistic.c.sim_card_id == sim_card.c.id,
                        sm_statistic.c.month == copy_month,
                    )
                )
            )
        )
        rows = self.session.execute(query).all()
        copied_data = [model.SIMMonthlyStatus(*row) for row in rows]
        return self.add_bulk_sim_monthly_statistics(copied_data)

    def imsi_reallocation_func(
        self,
        imsis: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> str:
        response_function = func.sim_reallocation(
            imsis,
            account_id,
            rate_plan_id,
        )
        query = select("*").select_from(response_function)
        message = self.session.execute(query).scalar_one()
        self.session.commit()
        return message

    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> int | None:
        sim_allocation = orm.sim_allocation

        query = (
            select(sim_allocation.c.account_id)
            .where(
                # and_(
                # or_(
                #     sim_allocation.c.account_id == account_id,
                #     account_id
                #     is None,  # This handles the case where account_id is None
                # ),
                sim_allocation.c.imsi.in_(imsi),
                # )
            )
            .group_by(sim_allocation.c.account_id)
        )
        logger.info(f"Query: {query}")
        logger.info(f"Account_id : {account_id}, imsi: {imsi}")
        try:
            return self.session.execute(query).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.warning(f"Database error: {e}")
            return None

    def get_allocated_imsi(self, imsi_list: list[IMSI]) -> Iterator[model.Allocation]:
        sim_allocation = orm.sim_allocation
        sim_allocation_details = orm.sim_allocation_details
        sim_range = orm.sim_range
        sim_card = orm.sim_card
        query = (
            select(
                sim_allocation_details.c.id,
                sim_allocation.c.title,
                sim_allocation.c.imsi,
                sim_card.c.iccid,
                sim_card.c.msisdn,
                sim_allocation.c.range_id,
                sim_allocation.c.rate_plan_id,
                sim_allocation.c.account_id,
                sim_allocation.c.allocation_details_id,
                sim_allocation.c.created_at,
                sim_range.c.form_factor,
            )
            .join(
                sim_allocation,
                sim_allocation_details.c.id == sim_allocation.c.allocation_details_id,
            )
            .join(
                sim_range,
                sim_allocation.c.range_id == sim_range.c.id,
            )
            .join(
                sim_card,
                sim_allocation.c.id == sim_card.c.allocation_id,
            )
            .where(sim_allocation.c.imsi.in_(imsi_list))
        )
        for row in self.session.execute(query).mappings():
            yield model.Allocation(**row)

    def get_rate_plan_by_imsi(self, imsi: IMSI) -> int:
        sim_allocation = orm.sim_allocation
        sim_card = orm.sim_card

        query = (
            select(
                func.coalesce(
                    sim_card.c.rate_plan_id, sim_allocation.c.rate_plan_id
                ).label("rate_plan_id")
            )
            .join(sim_card, sim_allocation.c.id == sim_card.c.allocation_id)
            .where(sim_allocation.c.imsi == imsi)
        )

        row = self.session.execute(query).mappings().first()

        return row["rate_plan_id"] if row else None

    def is_msisdn_already_exists(self, msisdn: MSISDN) -> bool:
        existing_sim = (
            self.session.query(model.SIMCard)
            .filter(model.SIMCard.msisdn == str(msisdn))
            .first()
        )
        return existing_sim is not None

    def is_imsi_exists(self, imsi: IMSI) -> bool:
        existing_sim = (
            self.session.query(model.SIMCard)
            .filter(model.SIMCard.imsi == str(imsi))
            .first()
        )
        return existing_sim is None

    def get_rate_plan_with_allocation_count(
        self, rate_plan_id: int
    ) -> model.RatePlanInfo:

        rate_plan = rate_plan_orm.RatePlan
        sim_allocation = orm.sim_allocation
        sim_card = orm.sim_card

        sim_alloc_alias = aliased(sim_allocation)
        sim_card_alias = aliased(sim_card)

        query = (
            select(
                rate_plan.account_id,
                rate_plan.sim_limit,
                func.count(sim_card_alias.c.id).label("allocated_sim_count"),
            )
            .select_from(rate_plan)
            .outerjoin(
                sim_alloc_alias,
                rate_plan.id == sim_alloc_alias.c.rate_plan_id,
            )
            .outerjoin(
                sim_card_alias,
                sim_alloc_alias.c.id == sim_card_alias.c.allocation_id,
            )
            .where(rate_plan.id == rate_plan_id)
            .group_by(rate_plan.id)
        )

        result = self.session.execute(query).one_or_none()

        if not result:
            return model.RatePlanInfo(allocated_sim_count=0, account_id=0, sim_limit=0)

        account_id, sim_limit, allocated_sim_count = result
        return model.RatePlanInfo(
            allocated_sim_count=allocated_sim_count or 0,
            account_id=account_id,
            sim_limit=sim_limit,
        )

    def get_allocation_count_by_rate_plan(self, id: int) -> int:
        sim_allocation = orm.sim_allocation
        sim_card = orm.sim_card

        query = (
            select(func.count())
            .select_from(sim_allocation)
            .join(
                sim_card,
                sim_allocation.c.id == sim_card.c.allocation_id,
            )
            .where(
                func.coalesce(sim_card.c.rate_plan_id, sim_allocation.c.rate_plan_id)
                == id
            )
        )
        result = self.session.execute(query).scalar_one()

        return result

    def allocation_count_by_account(self, id: int) -> int:
        sim_allocation = orm.sim_allocation

        query = (
            select(func.count())
            .select_from(sim_allocation)
            .where(sim_allocation.c.account_id == id)
        )
        result = self.session.execute(query).scalar_one()

        return result

    def get_msisdn_factor(
        self, msisdn_factor: model.MSISDNFactor, free_count: int | None = None
    ) -> list[MSISDN]:
        """Function to get Random MSISDN based on MSISDN Factor"""

        msisdn_pool = orm.msisdn_pool
        sim_card = orm.sim_card

        query = (
            select(msisdn_pool.c.msisdn)
            .where(
                and_(
                    msisdn_pool.c.msisdn_factor == msisdn_factor,
                    msisdn_pool.c.allocation_id.is_(None),
                    ~exists().where(sim_card.c.msisdn == msisdn_pool.c.msisdn),
                )
            )
            .order_by(func.random())
            .limit(free_count or 1)
        )
        result = self.session.execute(query).all()

        return [result.msisdn for result in result]

    def validate_sim_card_details_by_imsi(self, imsi: IMSI, msisdn: MSISDN):
        sim_card = orm.sim_card
        msisdn_pool = orm.msisdn_pool

        # Use a single session for all operations
        session = self.session

        # Lock the rows to ensure no other transactions can update the

        logger.info(f"IMSI: {imsi}, MSISDN: {msisdn}")

        # Create individual queries with row-level locking
        msisdn_query = (
            select(msisdn_pool.c.msisdn, msisdn_pool.c.sim_profile)
            .where(msisdn_pool.c.msisdn == msisdn)
            .with_for_update()
        )

        sim_msisdn_query = (
            select(sim_card.c.imsi)
            .where(sim_card.c.msisdn == msisdn)
            .limit(1)  # Place limit before with_for_update()
            .with_for_update()
        )

        sim_imsi_query = (
            select(sim_card.c.allocation_id, sim_card.c.msisdn)
            .where(sim_card.c.imsi == imsi)
            .limit(1)  # Place limit before with_for_update()
            .with_for_update()
        )

        # Execute all queries in a single transaction
        msisdn_result = session.execute(msisdn_query).one_or_none()
        sim_msisdn_result = session.execute(sim_msisdn_query).one_or_none()
        sim_imsi_result = session.execute(sim_imsi_query).one_or_none()

        # Process results
        existing_msisdn, existing_profile = (
            msisdn_result if msisdn_result else (None, None)
        )
        existing_imsi = sim_msisdn_result.imsi if sim_msisdn_result else None
        existing_allocation_id, existing_imsi_msisdn = (
            sim_imsi_result if sim_imsi_result else (None, None)
        )

        return (
            existing_msisdn,
            existing_allocation_id,
            existing_imsi,
            existing_profile,
            existing_imsi_msisdn,
        )

    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        sim_profile: model.SimProfile,
        allocation_id: int,
        existing_msisdn: MSISDN,
        existing_profile: model.SimProfile,
    ) -> bool:
        sim_card = orm.sim_card
        msisdn_pool = orm.msisdn_pool

        try:
            session = self.session

            if msisdn == existing_msisdn and sim_profile == existing_profile:
                return True
            elif msisdn == existing_msisdn and sim_profile != existing_profile:
                # Update msisdn_pool table
                session.query(msisdn_pool).filter(
                    msisdn_pool.c.msisdn == existing_msisdn
                ).update({msisdn_pool.c.sim_profile: sim_profile})
                session.commit()
                return True
            else:
                if allocation_id:
                    # Begin updating sim_card table
                    session.query(sim_card).filter(
                        and_(
                            sim_card.c.imsi == imsi,
                            sim_card.c.msisdn == existing_msisdn,
                        )
                    ).update({sim_card.c.msisdn: msisdn})

                    # Update msisdn_pool table
                    session.query(msisdn_pool).filter(
                        and_(
                            msisdn_pool.c.msisdn == msisdn,
                            msisdn_pool.c.allocation_id.is_(None),
                        )
                    ).update(
                        {
                            msisdn_pool.c.allocation_id: allocation_id,
                            msisdn_pool.c.sim_profile: sim_profile,
                        }
                    )

                    # Update msisdn_pool table
                    session.query(msisdn_pool).filter(
                        msisdn_pool.c.msisdn == existing_msisdn
                    ).update({msisdn_pool.c.allocation_id: None})

                    session.commit()
                    return True
                else:
                    return False
        except IntegrityError as e:
            logger.error(f"Integrity error {e}")
            session.rollback()
            return False

    def pool_msisdn_count(
        self,
        searching: Searching | None = None,
    ) -> int:
        search_term = searching.search if searching else None
        msisdn_pool_details_function = func.msisdn_pool_details_count(search_term)
        query = select("*").select_from(msisdn_pool_details_function)
        return self.session.execute(query).scalar_one()

    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:

        search_term = searching.search if searching else None
        msisdn_pool_details_function = func.msisdn_pool_details(
            # ordering.field if ordering else None,
            # ordering.order.lower() if ordering else None,
            pagination.offset if pagination else None,
            pagination.page_size if pagination else None,
            search_term,
        )

        msisdn_pool_query = select("*").select_from(msisdn_pool_details_function)

        result = self.session.execute(msisdn_pool_query).all()

        for row in result:
            yield model.MsisdnDetails(
                msisdn=row.msisdn,
                created_at=row.created_at,
                uploaded_by=row.uploaded_by,
                imsi=row.imsi,
                sim_profile=model.SimProfileData.get(row.sim_profile),  # type: ignore
                msisdn_factor=row.msisdn_factor,
                country=row.country,
                account_name=row.account_name,
                logo_key=row.logo_key,
            )

    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:

        msisdn_pool = orm.msisdn_pool
        sim_card = orm.sim_card

        query = (
            select(
                msisdn_pool.c.msisdn,
                msisdn_pool.c.created_at,
                msisdn_pool.c.uploaded_by,
                msisdn_pool.c.sim_profile,
                msisdn_pool.c.msisdn_factor,
            )
            .where(
                and_(
                    msisdn_pool.c.allocation_id.is_(None),
                    ~exists().where(sim_card.c.msisdn == msisdn_pool.c.msisdn),
                )
            )
            .order_by(msisdn_pool.c.created_at.desc())
        )

        if searching is not None:
            query = apply_search_to_sql(searching, model.MsisdnPool, query)

        result = self.session.execute(query).all()

        for row in result:
            yield model.MsisdnDetails(
                msisdn=row.msisdn,
                created_at=row.created_at,
                uploaded_by=row.uploaded_by,
                sim_profile=row.sim_profile,
                msisdn_factor=row.msisdn_factor,
            )

    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:

        msisdn_pool = orm.msisdn_pool
        sim_card = orm.sim_card

        query = select(
            func.count(msisdn_pool.c.id).label("total_count"),
            func.count(msisdn_pool.c.id)
            .filter(msisdn_pool.c.msisdn_factor == model.MSISDNFactor.NATIONAL)
            .label("national_count"),
            func.count(msisdn_pool.c.id)
            .filter(msisdn_pool.c.msisdn_factor == model.MSISDNFactor.INTERNATIONAL)
            .label("international_count"),
        ).where(
            msisdn_pool.c.allocation_id.is_(None),
            ~exists().where(sim_card.c.msisdn == msisdn_pool.c.msisdn),
        )

        response = self.session.execute(query).one()

        return model.MsisdnCountDetails(
            total_count=response.total_count,
            national=response.national_count,
            international=response.international_count,
        )

    def get_msisdn_in_pool_msisdn_table(self, msisdn_list):
        query = select(model.MsisdnPool.msisdn).where(
            model.MsisdnPool.msisdn.in_(msisdn_list)
        )
        result = self.session.execute(query).scalars().all()
        return result

    def get_msisdn_in_sim_card_table(self, msisdn_list):
        query = select(model.SIMCard.msisdn).where(
            model.SIMCard.msisdn.in_(msisdn_list),
        )
        result = self.session.execute(query).scalars().all()
        return result

    def upload_msisdn(self, msdidn_list: list[model.MsisdnPool]) -> None:
        self.session.add_all(msdidn_list)
        self.session.commit()

    def validate_bulk_sim_card_details(
        self,
        sim_card_list: list[dict],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[model.MSISDN],
    ) -> list[model.SimCardData]:
        """Validates a bulk list of IMSI and MSISDN pairs and returns SimCardData."""

        sim_card = orm.sim_card
        msisdn_pool = orm.msisdn_pool
        session = self.session

        logger.info(
            f"Validating IMSIs: {valid_imsi_list}, MSISDNs: {valid_msisdn_list}"
        )

        # Fetch msisdn details
        msisdn_query = select(
            msisdn_pool.c.msisdn, msisdn_pool.c.sim_profile, msisdn_pool.c.msisdn_factor
        ).where(msisdn_pool.c.msisdn.in_(valid_msisdn_list))

        # Fetch sim details
        sim_query = select(
            sim_card.c.imsi, sim_card.c.allocation_id, sim_card.c.msisdn
        ).where(
            or_(
                sim_card.c.msisdn.in_(valid_msisdn_list),
                sim_card.c.imsi.in_(valid_imsi_list),
            )
        )

        # Execute queries
        msisdn_results = session.execute(msisdn_query).fetchall()
        sim_results = session.execute(sim_query).fetchall()

        # Convert results into dictionaries for quick lookup
        msisdn_lookup = {row.msisdn: row.sim_profile for row in msisdn_results}
        factor_lookup = {row.msisdn: row.msisdn_factor for row in msisdn_results}
        sim_imsi_lookup = {row.msisdn: row.imsi for row in sim_results}
        sim_allocation_lookup = {
            row.imsi: (row.allocation_id, row.msisdn) for row in sim_results
        }

        validated_sim_cards = []
        for entry in sim_card_list:
            imsi = entry["IMSI"]
            msisdn = entry["MSISDN"]

            # Lookup values efficiently
            msisdn_sim_profile = msisdn_lookup.get(msisdn, None)
            msisdn_factor = factor_lookup.get(msisdn, None)
            existing_imsi = sim_imsi_lookup.get(msisdn, None)
            allocation_id, existing_msisdn = sim_allocation_lookup.get(
                imsi, (None, None)
            )

            # Append to results list
            validated_sim_cards.append(
                model.SimCardData(
                    requested_imsi=imsi,
                    requested_msisdn=msisdn,
                    msisdn_value=msisdn
                    if msisdn_sim_profile
                    else None,  # Only assign if profile exists
                    msisdn_sim_profile=msisdn_sim_profile,
                    msisdn_factor=msisdn_factor,
                    existing_imsi=existing_imsi,
                    allocation_id=allocation_id,
                    existing_msisdn=existing_msisdn,
                )
            )

        return validated_sim_cards

    def bulk_update_sim_card_details(
        self,
        all_sim_card_details: list[model.SimCardData],
        sim_profile: model.SimProfile,
    ) -> bool:
        """Bulk updates sim_card and msisdn_pool tables for multiple
        records in a single transaction without loops."""

        sim_card = orm.sim_card
        msisdn_pool = orm.msisdn_pool

        try:
            session = self.session

            # Bulk update sim_card table
            sim_card_update_stmt = (
                sim_card.update()
                .where(
                    and_(
                        sim_card.c.imsi == bindparam("requested_imsi"),
                        sim_card.c.msisdn == bindparam("existing_msisdn"),
                    )
                )
                .values(msisdn=bindparam("requested_msisdn"))
            )

            sim_card_update_data = [
                {
                    "requested_imsi": details.requested_imsi,
                    "existing_msisdn": details.existing_msisdn,
                    "requested_msisdn": details.requested_msisdn,
                }
                for details in all_sim_card_details
            ]

            if sim_card_update_data:
                session.execute(sim_card_update_stmt, sim_card_update_data)

            # Bulk update msisdn_pool to remove allocation_id for existing_msisdn
            existing_msisdn_list = [
                details.existing_msisdn for details in all_sim_card_details
            ]
            if existing_msisdn_list:
                session.execute(
                    msisdn_pool.update()
                    .where(msisdn_pool.c.msisdn.in_(existing_msisdn_list))
                    .values(allocation_id=None)
                )

            # Bulk update msisdn_pool table to set allocation_id where
            # requested_msisdn and sim_profile match
            msisdn_pool_allocation_stmt = (
                msisdn_pool.update()
                .where(
                    and_(
                        msisdn_pool.c.msisdn == bindparam("requested_msisdn"),
                        msisdn_pool.c.allocation_id.is_(None),
                    )
                )
                .values(
                    allocation_id=bindparam("allocation_id"), sim_profile=sim_profile
                )
            )

            msisdn_pool_allocation_data = [
                {
                    "requested_msisdn": details.requested_msisdn,
                    "allocation_id": details.allocation_id,
                }
                for details in all_sim_card_details
                if details.allocation_id
            ]

            if msisdn_pool_allocation_data:
                session.execute(
                    msisdn_pool_allocation_stmt, msisdn_pool_allocation_data
                )

            # Commit transaction
            session.commit()
            return True

        except IntegrityError as e:
            logger.error(f"Integrity error: {e}")
            session.rollback()
            return False

    def bulk_update_msisdn_pool(
        self,
        imsis: list[IMSI],
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        same_factor_allocation: bool,
        msisdns: list[MSISDN] | None = [],
    ) -> bool:
        msisdn_pool = orm.msisdn_pool
        sim_card = orm.sim_card

        if same_factor_allocation is False:
            cte = select(
                func.unnest(imsis).label("imsi"), func.unnest(msisdns).label("msisdn")
            ).cte("c")

            sim_update_stmt = (
                update(sim_card)
                .values(msisdn=cte.c.msisdn)
                .where(sim_card.c.imsi == cte.c.imsi)
            )

            pool_update_stmt = (
                update(msisdn_pool)
                .values(
                    allocation_id=sim_card.c.allocation_id,
                    sim_profile=sim_profile,
                )
                .where(
                    and_(
                        msisdn_pool.c.msisdn == sim_card.c.msisdn,
                        msisdn_pool.c.msisdn.in_(msisdns),
                    )
                )
            )

            self.session.execute(sim_update_stmt)
            self.session.execute(pool_update_stmt)
        else:
            msisdn_pool_update_stmt = (
                msisdn_pool.update()
                .where(
                    and_(
                        sim_card.c.msisdn == msisdn_pool.c.msisdn,
                        sim_card.c.imsi.in_(imsis),
                        msisdn_pool.c.allocation_id.is_(None),
                    )
                )
                .values(
                    sim_profile=sim_profile,
                    allocation_id=sim_card.c.allocation_id,
                )
            )
            self.session.execute(msisdn_pool_update_stmt)
        self.session.commit()
        return True

    def get_invalid_sim_and_msisdn_count(self) -> model.InvalidSimMsisdnCountDetails:

        sim_card_msisdn_pool_details_function = (
            func.get_sim_card_msisdn_pool_details_count()
        )

        sim_card_msisdn_pool_details_query = select("*").select_from(
            sim_card_msisdn_pool_details_function
        )
        result = self.session.execute(sim_card_msisdn_pool_details_query).one()

        return model.InvalidSimMsisdnCountDetails(**result)  # type: ignore

    def get_available_msisdn(
        self, imsi_list: list[IMSI], msisdn_factor: model.MSISDNFactor
    ) -> list[MSISDN]:
        msisdn_pool = orm.msisdn_pool
        sim_card = orm.sim_card
        query = (
            select(msisdn_pool.c.msisdn)
            .join(sim_card, sim_card.c.msisdn == msisdn_pool.c.msisdn)
            .where(
                and_(
                    msisdn_pool.c.allocation_id.is_(None),
                    sim_card.c.imsi.in_(imsi_list),
                    msisdn_pool.c.msisdn_factor == msisdn_factor,
                )
            )
        )
        result = self.session.execute(query).scalars().all()
        return result

    def validate_msisdn_update_request(
        self, msisdn_list: list[MSISDN], validation_key: str
    ):
        try:
            msisdn_pool = orm.msisdn_pool
            validation_column = getattr(msisdn_pool.c, validation_key)
            query = (
                select(validation_column)
                .where(
                    msisdn_pool.c.msisdn.in_(msisdn_list),
                )
                .group_by(validation_column)
            )
            response = self.session.execute(query).scalar_one()
            if response:
                return True
        except SQLAlchemyError as e:
            logger.error(f"Database error: {e}")
            return False

    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        sim_card = orm.sim_card
        msisdn_pool = orm.msisdn_pool
        allocation = orm.sim_allocation
        range_table = orm.sim_range

        try:
            allocation_id_query = select(sim_card.c.allocation_id).where(
                sim_card.c.imsi.in_(imsi_list), sim_card.c.allocation_id.isnot(None)
            )

            allocation_ids = self.session.execute(allocation_id_query).scalars().all()

            allocation_ids = [aid for aid in allocation_ids if aid is not None]
            if not allocation_ids:
                return model.UnallocateSimCardDetails(
                    message=f"0 out of {len(imsi_list)} IMSIs were unallocated"
                )

            affected_imsis = (
                self.session.query(sim_card)
                .filter(sim_card.c.allocation_id.in_(allocation_ids))
                .update({sim_card.c.allocation_id: None})
            )

            self.session.query(msisdn_pool).filter(
                msisdn_pool.c.allocation_id.in_(allocation_ids)
            ).update({msisdn_pool.c.allocation_id: None})

            self.session.query(allocation).filter(
                allocation.c.id.in_(allocation_ids)
            ).delete()

            range_counts = (
                self.session.query(sim_card.c.range_id, func.count().label("count"))
                .filter(
                    sim_card.c.allocation_id.is_(None), sim_card.c.imsi.in_(imsi_list)
                )
                .group_by(sim_card.c.range_id)
                .all()
            )
            for range_id, range_count in range_counts:
                self.session.query(range_table).filter(
                    range_table.c.id == range_id
                ).update(
                    {range_table.c.remaining: range_table.c.remaining + range_count},
                    synchronize_session=False,
                )

            self.session.commit()

            return model.UnallocateSimCardDetails(
                message=f"{affected_imsis} out of {len(imsi_list)} IMSIs unallocated"
            )

        except IntegrityError as e:
            logger.error(f"Integrity error: {e}")
            self.session.rollback()
            raise UnallocationException("Failed to unallocate SIM card.")

    def imsis_to_delete(self, imsis_list: list[IMSI]) -> dict[str, int]:
        try:
            sim_card = orm.sim_card
            sim_monthly_statistic = orm.active_sim_monthly_statistic

            # Step 1: Fetch existing IMSIs and their allocation status in one query
            imsi_allocation_map = dict(
                self.session.query(sim_card.c.imsi, sim_card.c.allocation_id)
                .filter(sim_card.c.imsi.in_(imsis_list))
                .all()
            )

            existing_imsis = set(imsi_allocation_map.keys())
            deletable_imsis = {
                imsi
                for imsi, alloc_id in imsi_allocation_map.items()
                if alloc_id is None
            }

            deleted_count = 0

            if deletable_imsis:
                # Get sim_card IDs for deletable IMSIs
                sim_card_query = select(sim_card.c.id).where(
                    sim_card.c.imsi.in_(deletable_imsis)
                )
                sim_card_ids = self.session.execute(sim_card_query).scalars().all()

                # Step 3: Delete related entries from sim_monthly_statistic
                if sim_card_ids:
                    self.session.query(sim_monthly_statistic).filter(
                        sim_monthly_statistic.c.sim_card_id.in_(sim_card_ids)
                    ).delete(synchronize_session=False)

                # Step 4: Delete from sim_card table
                deleted_count = (
                    self.session.query(sim_card)
                    .filter(sim_card.c.imsi.in_(deletable_imsis))
                    .delete(synchronize_session=False)
                )

                # Step 5: Update sim_range (quantity and remaining)
                range_update_counts: dict[int, int] = defaultdict(int)
                for imsi in deletable_imsis:
                    range_row = (
                        self.session.query(orm.sim_range)
                        .filter(
                            and_(
                                orm.sim_range.c.imsi_first <= imsi,
                                orm.sim_range.c.imsi_last >= imsi,
                            )
                        )
                        .first()
                    )
                    if range_row:
                        range_update_counts[range_row.id] += 1

                for range_id, count_ in range_update_counts.items():
                    self.session.query(orm.sim_range).filter(
                        orm.sim_range.c.id == range_id
                    ).update(
                        {
                            orm.sim_range.c.quantity: func.greatest(
                                orm.sim_range.c.quantity - count_, 0
                            ),
                            orm.sim_range.c.remaining: func.greatest(
                                orm.sim_range.c.remaining - count_, 0
                            ),
                        }
                    )

            self.session.commit()

            return {
                "deleted_imsis_count": deleted_count,
                "not_found_imsis_count": len(set(imsis_list) - existing_imsis),
            }
        except SQLAlchemyError as e:
            logger.error(f"General SQLAlchemy error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error during IMSI deletion: {str(e)}")
            raise

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        try:
            order_data = model.Order(notes=order.notes, order_date=datetime.now())
            self.session.add(order_data)
            self.session.flush()

            customer_details = order.customer_details
            customer_details.order_id = order_data.uuid
            self.session.add(customer_details)

            shipping_details = order.shipping_details
            shipping_details.order_id = order_data.uuid
            self.session.add(shipping_details)

            order_status_history = model.OrderStatusHistory(
                order_id=order_data.uuid,
                status_name="PENDING",
                status_date=datetime.now(),
            )
            self.session.add(order_status_history)

            order_item_array = []
            for order_item in order.order_items:
                order_item_array.append(
                    model.OrderItem(
                        order_id=order_data.uuid,
                        sim_type=order_item.sim_type,
                        quantity=order_item.quantity,
                    )
                )

            self.session.add_all(order_item_array)
            self.session.commit()

            return model.OrderResponse(
                order_uuid=order_data.uuid,
                message="Order created successfully",
            )
        except SQLAlchemyError as e:
            logger.error(f"Error creating order: {e}")
            self.session.rollback()
            raise
