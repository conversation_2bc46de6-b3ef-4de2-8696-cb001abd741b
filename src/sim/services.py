import datetime
import io
import uuid
from abc import ABC, abstractmethod
from collections import Counter
from operator import attrgetter
from typing import BinaryIO, Iterable, Iterator, Type
from uuid import UUID, uuid4

from defusedxml.lxml import fromstring
from fastapi import UploadFile
from pandas import DataFrame
from PIL import Image, UnidentifiedImageError
from pydantic import AnyHttpUrl, EmailStr
from sqlalchemy.exc import IntegrityError
from svgutils.transform import SVGFigure

from app.config import logger
from auth.exceptions import NotFound
from cdrdata.adapters.repository import AbstractCdrRepository
from common.file_storage import AbstractFileStorage
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import BaseCSVParser
from common.searching import Searching
from common.types import ICCID, IMSI
from common.types import MSISDN as msisdn_type
from common.types import ContentType, FormFactor, ImageFormat, Month
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from redis.adapters.externalapi import HTT<PERSON>edisAPI
from sim import exceptions
from sim.adapters.repository import AbstractSimRepository
from sim.domain import model
from sim.domain.model import (
    MSISDN,
    MarketShareData,
    MarketShareModel,
    MarketSharePeriod,
    MarketShareUsage,
    MSISDNFactor,
    NotificationStatus,
    RequestType,
    SIMCardProviderAudit,
    SIMMonthlyStatus,
    SimProfileDetails,
    SimStatus,
)
from sim.domain.ports import (
    AbstractAuditService,
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
)
from sim.parser import FileNameParser, ImsiCSVParser, SIMCardCSVParser


class MediaService:
    def __init__(self, file_storage: AbstractFileStorage):
        self.file_storage = file_storage

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise exceptions.MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise exceptions.MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"
        self.file_storage.upload_file(
            filename=image_key,
            file_obj=file_obj,
            content_type=content_type.mime_type,
        )
        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        url = self.file_storage.generate_file_url(file_key)
        return AnyHttpUrl(url, scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if not self.file_storage.file_exists(file_key):
            raise exceptions.MediaError(f"File not found: {file_key}.")


class FakeMediaService:
    def __init__(self):
        self.files: dict[str, dict[str, bytes]] = {}

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise exceptions.MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise exceptions.MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"

        self.files[image_key] = {
            "content": file_obj,
            "content_type": content_type.mime_type.encode(),
        }

        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        return AnyHttpUrl(f"https://fake-storage/{file_key}", scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if file_key not in self.files:
            raise exceptions.MediaError(f"File not found: {file_key}.")


class AbstractSimService(ABC):
    @abstractmethod
    def create_empty_range(
        self, title: str, form_factor: FormFactor, created_by: str
    ) -> model.Range:
        ...

    @abstractmethod
    def create_range(
        self,
        title: str,
        form_factor: FormFactor,
        created_by: str,
        file: BinaryIO,
        client_ip: str,
        parser_impl: Type[BaseCSVParser] = SIMCardCSVParser,
    ) -> model.Range:
        ...

    @abstractmethod
    def remove_range(self, range_id: int) -> None:
        ...

    @abstractmethod
    def get_ranges(self) -> Iterator[model.Range]:
        ...

    @abstractmethod
    def get_allocations(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        ...

    @abstractmethod
    def add_allocation(self, allocation: model.Allocation) -> model.Allocation:
        ...

    @abstractmethod
    def remove_allocation(self, allocation_id: int) -> None:
        ...

    @abstractmethod
    def remove_allocations_by_range_id(self, range_id: int) -> None:
        ...

    @abstractmethod
    def get_sim_remains(self) -> Iterable[tuple[str, FormFactor, int]]:
        ...

    @abstractmethod
    def get_sim_cards(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        ...

    @abstractmethod
    def get_sim_cards_export(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
    ) -> Iterator[model.SIMCard]:
        ...

    @abstractmethod
    def get_sim_count(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
    ) -> int:
        ...

    @abstractmethod
    def sim_status(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMStatusResponse:
        ...

    @abstractmethod
    def activate_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMActivateResponse:
        ...

    @abstractmethod
    def suspend_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMDeactivatedResponse:
        ...

    @abstractmethod
    def audit_logs(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAuditLogs], int]:
        ...

    @abstractmethod
    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        ...

    @abstractmethod
    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.SimUsage], int, dict]:
        ...

    @abstractmethod
    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.ActiveSimMonthlyStatistic], int]:
        ...

    @abstractmethod
    def get_sim_usage_export(
        self,
        account_id: int,
    ) -> Iterator[model.SimUsage]:
        ...

    @abstractmethod
    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimCDRHistory], int]:
        ...

    @abstractmethod
    def connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimCDRHistory]:
        pass

    @abstractmethod
    def get_month_usage(
        self,
        account_id: int | None = None,
    ) -> model.MonthUsage:
        ...

    @abstractmethod
    def sim_status_details(
        self,
        sim_status: SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> tuple[Iterator[model.SimStatusDetails], int]:
        ...

    @abstractmethod
    def update_sim_card_by_imsi(
        self,
        imsi: IMSI,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def bulk_background_process(
        self,
        imsi: list[IMSI],
        created_by: str,
        sim_status: model.SimStatus,
        client_ip: str | None = None,
    ) -> None:
        ...

    @abstractmethod
    def copy_monthly_statistics(self) -> int:
        ...

    @abstractmethod
    def get_imsis(
        self,
        iccid: list[ICCID],
    ) -> Iterator[model.IMSIDetails]:
        ...

    @abstractmethod
    def get_market_share_by_account(
        self, market_share_data: MarketShareData
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def get_market_share(self, period: MarketSharePeriod) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimVoiceCDRHistory], int]:
        ...

    @abstractmethod
    def voice_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:
        pass

    @abstractmethod
    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimSMSCDRHistory], int]:
        ...

    @abstractmethod
    def sms_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:
        pass

    @abstractmethod
    def get_market_share_imsi(
        self, period: MarketSharePeriod, imsi: IMSI
    ) -> model.MarketShareCarrier:
        ...

    @abstractmethod
    def custom_imsi_allocation(
        self,
        title: str,
        account_id: int,
        rate_plan_id: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        file_name: str,
        file: BinaryIO,
        client_ip: str,
        created_by: str,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
    ) -> model.AllocationResult:
        ...

    @abstractmethod
    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> bool:
        ...

    @abstractmethod
    def re_allocation_validation(
        self,
        imsi_list,
        account_id: int,
        rate_plan_id: int,
    ) -> tuple[model.ReAllocationResult, list[int]]:
        ...

    @abstractmethod
    def rate_plan_change_sim_validation(
        self,
        imsi,
        account_id: int,
        rate_plan_id: int,
    ) -> model.RatePlanChangeSimLimitResult:
        ...

    @abstractmethod
    def re_allocation(
        self,
        account_id: int,
        rate_plan_id: int,
        imsi_list: list[IMSI],
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def update_msisdn(self, excel_data):
        ...

    @abstractmethod
    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        client_ip: str,
        created_by: str | None = None,
    ) -> model.UpdateSimCardDetailsResult:
        ...

    @abstractmethod
    def get_msisdn_factor(self, msisdn_factor: model.MSISDNFactor) -> str:
        ...

    @abstractmethod
    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.MsisdnDetails], int]:
        ...

    @abstractmethod
    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        ...

    @abstractmethod
    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        ...

    @abstractmethod
    def upload_msisdn(
        self,
        msisdn_list: list[MSISDN],
        invalid_format: list[str],
        duplicate_msisdn: list[MSISDN],
        client_ip: str,
        uploaded_by: str | None = None,
    ) -> model.MsisdnResult:
        ...

    @abstractmethod
    def bulk_update_sim_card_details(
        self,
        total_records: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
        duplicate_imsi: list[IMSI],
        duplicate_msisdn: list[MSISDN],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[MSISDN],
        client_ip: str,
        valid_data,
        uploaded_by: str | None = None,
    ) -> model.BulkSimCardUpdateResult:
        ...

    @abstractmethod
    def update_sim_card_details(
        self,
        sim_profile: model.SimProfile,
        total_records: int,
        client_ip: str,
        msisdn_factor: model.MSISDNFactor,
        uploaded_by: str | None = None,
        imsi_list: list[IMSI] = [],
        msisdn_list: list[msisdn_type] = [],
        duplicate_imsi: list[IMSI] = [],
        duplicate_msisdn: list[MSISDN] = [],
        valid_data: list[dict] = [],
        invalid_data: list[dict] = [],
    ):
        ...

    @abstractmethod
    def validate_common_request(self, msisdn_list: list[msisdn_type]) -> bool:
        ...

    @abstractmethod
    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        ...

    @abstractmethod
    def imsis_to_delete(self, imsi_list: list[IMSI]) -> model.IMSIDeleteResponse:
        ...

    @abstractmethod
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...


class SimService(AbstractSimService):
    def __init__(
        self,
        sim_repository: AbstractSimRepository,
        rate_plan_repository: AbstractRatePlanRepository,
        provisioning: AbstractSIMProvisioningAPI,
        cdr_repository: AbstractCdrRepository,
        market_share_api: AbstractMarketShareAPI,
        audit_service: AbstractAuditService,
        media_service: MediaService,
        redis_api: HTTPRedisAPI,
    ):
        self.sim_repository = sim_repository
        self.rate_plan_repository = rate_plan_repository
        self.provisioning = provisioning
        self.cdr_repository = cdr_repository
        self.market_share_api = market_share_api
        self.audit_service = audit_service
        self.media = media_service
        self.redis_api = redis_api

    @staticmethod
    def _validate_sim_cards(sim_cards: Iterable[model.SIMCard]) -> list[model.SIMCard]:
        sorted_sim_cards = list(sorted(sim_cards, key=attrgetter("imsi")))
        imsi_list = [s.imsi for s in sorted_sim_cards]
        non_unique_imsi = [k for (k, v) in Counter(imsi_list).items() if v > 1]
        if non_unique_imsi:
            raise exceptions.ImsisAreNotUnique(
                f"Non-unique IMSI(s) found in CSV file: {', '.join(non_unique_imsi)}"
            )

        msisdn_list = [s.msisdn for s in sorted_sim_cards]
        duplicate_msisdn = [k for (k, v) in Counter(msisdn_list).items() if v > 1]
        if duplicate_msisdn:
            raise exceptions.ImsisAreNotUnique(
                f"Non-unique MSISDN(s) found in CSV file: {', '.join(duplicate_msisdn)}"
            )

        imsi_first, imsi_last = sorted_sim_cards[0].imsi, sorted_sim_cards[-1].imsi
        not_continuity_imsi = set(range(int(imsi_first), int(imsi_last) + 1)) - set(
            map(int, imsi_list)
        )
        if not_continuity_imsi:
            raise exceptions.ImsisAreNotContinuity(
                f"There is no continuous range of IMSI in the CSV file, "
                f"missing IMSI(s): {', '.join(map(str, not_continuity_imsi))}"
            )
        return sorted_sim_cards

    def _check_sim_limit(
        self, sim_count: int, rate_plan_sim_count: int, sim_limit: int | None = None
    ):

        if sim_limit and (sim_count > sim_limit):
            allowed_sim_to_allocate = sim_limit - rate_plan_sim_count
            raise exceptions.SimLimitCountError(
                f"Unable to assign SIMs. There are currently {rate_plan_sim_count}"
                " SIMs assigned to this rate plan."
                f" {allowed_sim_to_allocate} SIMs can be added"
            )

    def create_empty_range(
        self, title: str, form_factor: FormFactor, created_by: str
    ) -> model.Range:
        range_ = model.Range(
            title=title,
            form_factor=form_factor,
            created_by=created_by,
        )
        self.sim_repository.add_range(range_)
        return range_

    def remove_range(self, range_id: int) -> None:
        range_ = self.sim_repository.get_range_by_id(range_id)
        if range_ is None:
            raise exceptions.RangeDoesNotExist(range_id)
        self.sim_repository.remove_range(range_)

    def _create_msisdn_pool(
        self, msisdns: list[MSISDN], uploaded_by: str
    ) -> list[model.MsisdnPool]:
        """
        MSISDN Factor will be set to NATIONAL default
        NATIONAL if msisdn starts with 447 or any other number
        INTERNATIONAL if msisdn starts with 883
        """
        return [
            model.MsisdnPool(
                msisdn=msisdn,
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn_factor=MSISDNFactor.INTERNATIONAL
                if msisdn.startswith("883")
                else MSISDNFactor.NATIONAL,
                uploaded_by=uploaded_by,
                created_at=datetime.datetime.today(),
            )
            for msisdn in msisdns
        ]

    def create_range(
        self,
        title: str,
        form_factor: FormFactor,
        created_by: str,
        file: BinaryIO,
        client_ip: str,
        parser_impl: Type[BaseCSVParser] = SIMCardCSVParser,
    ) -> model.Range:
        sim_cards = self._validate_sim_cards(parser_impl(file=file))
        imsi_first, imsi_last = sim_cards[0].imsi, sim_cards[-1].imsi
        if self.sim_repository.check_imsi_range(imsi_first, imsi_last):
            logger.error(
                f"SIM cards already exist for range from {imsi_first} to {imsi_last}."
            )
            raise exceptions.SimCardImsiAlreadyExist(
                f"SIM cards already exist for range from {imsi_first} to {imsi_last}"
            )

        unique_msisdns = list({sim_card.msisdn for sim_card in sim_cards})
        existing_in_pool = set(
            self.sim_repository.get_msisdn_in_pool_msisdn_table(
                msisdn_list=unique_msisdns
            )
        )
        existing_in_simcard = set(
            self.sim_repository.get_msisdn_in_sim_card_table(msisdn_list=unique_msisdns)
        )
        already_existing_msisdns = existing_in_pool | existing_in_simcard
        if already_existing_msisdns:
            logger.error(
                "Msisdn alreadt exist error:- "
                f"existing in Msisdn pool: {existing_in_pool}"
                f"existing in Sim Card: {existing_in_simcard}"
            )
            raise exceptions.MSISDNExitError(
                "The following MSISDN(s) already exist in the system: "
                f"{', '.join(already_existing_msisdns)}"
            )

        range_ = model.Range(
            title=title,
            form_factor=form_factor,
            created_by=created_by,
            imsi_first=imsi_first,
            imsi_last=imsi_last,
        )
        range_.sim_cards = sim_cards
        pool_msisdn = self._create_msisdn_pool(
            msisdns=unique_msisdns, uploaded_by=created_by
        )
        self.sim_repository.add_range(range_, pool_msisdn)
        return range_

    def get_ranges(self) -> Iterator[model.Range]:
        yield from self.sim_repository.get_range_list()

    def _get_logo_url(self, logo_key: str | None) -> AnyHttpUrl | None:
        try:
            if logo_key is None:
                return logo_key
            return self.media.get_file_url(logo_key)
        except Exception as e:
            logger.error(str(e), exc_info=True)
            return None

    def get_allocations(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        response, total_count = self.sim_repository.add_allocations_details(
            pagination=pagination
        )
        if not response:
            raise exceptions.NotFound("No data found")
        for allocation in response:
            allocation.logo_url = self._get_logo_url(allocation.logo_key)
        return response, total_count

    def add_allocation(self, allocation: model.Allocation) -> model.Allocation:
        if allocation.rate_plan_id is None:
            raise AssertionError("The allocation must have an assigned rate_plan")

        rate_plan = self.rate_plan_repository.get(allocation.rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{allocation.rate_plan_id} does not exist"
            )

        if rate_plan.account_id != allocation.account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan"
            )
        range_ = self.sim_repository.get_range_by_id(allocation.range_id)

        if range_ is None:
            raise exceptions.RangeDoesNotExist(allocation.range_id)

        allocation = range_.allocate(allocation)
        self.sim_repository.update_range(range_)
        self.sim_repository.update_sim_cards_with_allocation(allocation)
        return allocation

    def remove_allocations_by_range_id(self, range_id: int) -> None:
        range_ = self.sim_repository.get_range_by_id(range_id)
        if range_ is None:
            raise exceptions.RangeDoesNotExist(range_id)
        return self.sim_repository.remove_allocations_in_range(range_)

    def remove_allocation(self, allocation_id: int) -> None:
        allocation = self.sim_repository.get_allocation_by_id(allocation_id)
        if allocation is None:
            raise exceptions.AllocationDoesNotExist(allocation_id)
        last_allocation = self.sim_repository.get_last_allocation_in_range(
            allocation.range_id
        )
        if last_allocation is None:
            raise exceptions.AllocationDoesNotExist(allocation_id)
        return self.sim_repository.remove_allocation(allocation)

    def get_sim_remains(self) -> Iterable[tuple[str, FormFactor, int]]:
        for form_factor, remaining in self.sim_repository.get_sim_remains():
            yield "NR", form_factor, remaining

    def get_sim_cards(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        yield from self.sim_repository.get_sim_cards(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            pagination=pagination,
            ordering=ordering,
            imsi_list=imsi_list,
            creation_month=creation_month,
            searching=searching,
        )

    def get_sim_cards_export(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
    ) -> Iterator[model.SIMCard]:
        yield from self.sim_repository.get_sim_cards(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            imsi_list=imsi_list,
            creation_month=creation_month,
        )

    def get_sim_count(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
        deactivated_only: bool = False,
        pending_only: bool = False,
        ready_activation_only: bool = False,
        sim_status: SimStatus | None = None,
    ) -> int:
        return self.sim_repository.get_sim_count(
            account_id=account_id,
            rate_plan_ids=rate_plan_ids,
            creation_month=creation_month,
            imsi_list=imsi_list,
            searching=searching,
            active_only=active_only,
            deactivated_only=deactivated_only,
            pending_only=pending_only,
            ready_activation_only=ready_activation_only,
            sim_status=sim_status,
        )

    def get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
            if sim_card.allocation_id is None:
                logger.error(f"IMSI allocation not found:- {imsi}")
                raise exceptions.AllocationError("IMSI allocation not found.")
            if sim_card.sim_profile is None:
                logger.error(f"Sim-MSISDN sim_profile not found:- {imsi}")
                raise exceptions.SimError("Sim profile not found.")
        except StopIteration:
            logger.error(f"Requested IMSI not found:- {imsi}")
            raise exceptions.SimCardsNotFound("Requested IMSI not found.")
        return sim_card

    def sim_status(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMStatusResponse:
        """Function for get SIM status"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        pip_response = self.provisioning.sim_status(IMSI(imsi), MSISDN(msisdn))
        if (SimStatus(sim_card.sim_status) != pip_response.sim_status) and (
            sim_card.sim_status != SimStatus.PENDING
        ):
            if (SimStatus(sim_card.sim_status) != SimStatus.READY_FOR_ACTIVATION) or (
                SimStatus(sim_card.sim_status) == SimStatus.READY_FOR_ACTIVATION
                and pip_response.sim_status != SimStatus.DEACTIVATED
            ):

                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Get status",
                    prior_value=SimStatus(sim_card.sim_status),
                    new_value=SimStatus(pip_response.sim_status),
                    field="Status",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                    audit_date=datetime.datetime.today(),
                    message="System",
                    status=pip_response.sim_status,
                    work_id=imsi,
                    prior_status=sim_card.sim_status,
                )
                result = self.audit_service.add_sim_audit_api([audit_detail])

                sim_provider_log = SIMCardProviderAudit(
                    sim_activity_log_uuid=audit_detail.uuid,
                    activity_id=str(result["id"][0]),
                    audit_date=datetime.datetime.today(),
                    message="System",
                    status=pip_response.sim_status,
                    work_id=imsi,
                    prior_status=sim_card.sim_status,
                )

                self.sim_repository.update_sim_card(
                    imsi, SimStatus(pip_response.sim_status)
                )
                sim_monthly_status = self._get_sim_monthly_status(
                    IMSI(imsi), sim_provider_log
                )
                if SimStatus(sim_card.sim_status) == SimStatus.READY_FOR_ACTIVATION:
                    sim_monthly_status.is_first_activation = False
                self._add_sim_monthly_status(
                    sim_monthly_status,
                    SimStatus(pip_response.sim_status),
                )
        return pip_response

    def activate_sim_using_provisioning(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        valid_sim_profile: str,
        client_ip: str | None = None,
    ):
        """Activate the SIM using the provisioning object and log the result"""
        ppl_provision = self.provisioning.activate_sim(
            imsi, MSISDN(msisdn), SimStatus(prior_status), valid_sim_profile, client_ip
        )
        if ppl_provision.status == "Invalid":
            raise exceptions.SimActivationError("SIM already activated.")
        sim_status = SimStatus.PENDING
        self.sim_repository.update_sim_card(IMSI(imsi), SimStatus(sim_status))
        return ppl_provision

    def create_sim_response(
        self, audit_uuid: str, ppl_provision: model.SIMActivateResponse
    ) -> model.SIMActivateResponse:
        """Create SIM activation response object using the provisioning result"""
        sim_response = model.SIMActivateResponse(
            uuid=audit_uuid,
            message=ppl_provision.message,
            status=ppl_provision.status,
        )
        return sim_response

    def _validate_sim_profile_code(self, sim_profile: model.SimProfile) -> str:
        valid_sim_profile = SimProfileDetails.get(sim_profile.name)
        if not valid_sim_profile:
            logger.error(
                f"Requested Sim Profile:- {sim_profile} ,"
                "Invalid Sim Profile requested"
            )
            raise exceptions.SimAccountProfileError("Invalid Sim Profile requested")
        return valid_sim_profile

    def activate_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ) -> model.SIMActivateResponse:
        """Function for activating SIM"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        prior_status = sim_card.sim_status
        sim_profile = sim_card.sim_profile
        logger.debug(f"Sim Profile:- {sim_profile}")

        valid_sim_profile = self._validate_sim_profile_code(sim_profile=sim_profile)

        audit_detail = model.SIMCardAudit(
            uuid=uuid4(),
            imsi=IMSI(imsi),
            iccid=ICCID(iccid),
            msisdn=MSISDN(msisdn),
            request_type=model.RequestType.PROVIDE.name,
            prior_value=SimStatus(sim_card.sim_status),
            new_value=SimStatus.ACTIVE,
            field="Status",
            action="Updated",
            client_ip=client_ip,
            created_by=created_by,
        )
        result = self.audit_service.add_sim_audit_api([audit_detail])

        ppl_provision = self.activate_sim_using_provisioning(
            imsi, MSISDN(msisdn), prior_status, valid_sim_profile, client_ip
        )

        audit_provider_detail = model.SIMCardProviderAudit(
            sim_activity_log_uuid=audit_detail.uuid,
            activity_id=str(result["id"][0]),
            audit_date=ppl_provision.audit_date,
            message=ppl_provision.message,
            status=ppl_provision.status,
            work_id=ppl_provision.work_id,
            prior_status=ppl_provision.prior_status,
        )
        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        sim_response = self.create_sim_response(result["id"][0], ppl_provision)
        return sim_response

    def deactivate_sim_using_suspend(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        prior_status: SimStatus,
        client_ip: str | None = None,
    ):
        """Deactivate the SIM using the suspend object and log the result"""
        ppl_suspend = self.provisioning.suspend_sim(
            MSISDN(msisdn), SimStatus(prior_status), client_ip
        )
        if ppl_suspend.status == "Error":
            raise exceptions.SimDeActivationError("SIM already deactivated.")
        sim_status = SimStatus.PENDING
        self.sim_repository.update_sim_card(IMSI(imsi), SimStatus(sim_status))
        return ppl_suspend

    def create_sim_response_suspend(
        self, audit_uuid: str, ppl_suspend: model.SIMDeactivatedResponse
    ) -> model.SIMDeactivatedResponse:
        """Create SIM deactivation response object using the suspend result"""
        sim_response = model.SIMDeactivatedResponse(
            uuid=audit_uuid,
            message=ppl_suspend.message,
            status=ppl_suspend.status,
        )
        return sim_response

    def suspend_sim(
        self, imsi: IMSI, created_by: str, client_ip: str | None = None
    ) -> model.SIMDeactivatedResponse:
        """Function for suspend SIM"""
        sim_card = self.get_sim_card(IMSI(imsi))
        iccid = sim_card.iccid
        msisdn = sim_card.msisdn
        prior_status = sim_card.sim_status

        audit_detail = model.SIMCardAudit(
            uuid=uuid4(),
            imsi=IMSI(imsi),
            iccid=ICCID(iccid),
            msisdn=MSISDN(msisdn),
            request_type=model.RequestType.CEASE.name,
            prior_value=SimStatus(sim_card.sim_status),
            new_value=SimStatus.DEACTIVATED,
            field="Status",
            action="Updated",
            client_ip=client_ip,
            created_by=created_by,
        )
        result = self.audit_service.add_sim_audit_api([audit_detail])

        ppl_suspend = self.deactivate_sim_using_suspend(
            IMSI(imsi), MSISDN(msisdn), prior_status, client_ip
        )

        audit_provider_detail = model.SIMCardProviderAudit(
            sim_activity_log_uuid=audit_detail.uuid,
            activity_id=str(result["id"][0]),
            audit_date=ppl_suspend.audit_date,
            message=ppl_suspend.message,
            status=ppl_suspend.status,
            work_id=ppl_suspend.work_id,
            prior_status=ppl_suspend.prior_status,
        )
        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        sim_response = self.create_sim_response_suspend(result["id"][0], ppl_suspend)
        return sim_response

    def audit_logs(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAuditLogs], int]:
        """Function for audit logs of activate and deactivate"""

        audit_logs = self.audit_service.get_sim_audit_api(
            imsi=imsi,
            month=month,
            is_client=is_client,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        if audit_logs.totalCount == 0:
            raise exceptions.NoAuditLogs(imsi, month)
        return audit_logs.results, audit_logs.totalCount

    def get_connection_summary(self, imsi: IMSI) -> model.ConnectionSummary:
        last_session = self.cdr_repository.get_cdr_last_session(imsi)
        connection_history_summary = self.sim_repository.get_connection_summary(imsi)
        if connection_history_summary.rate_plan_id is not None:
            get_rate_plan = self.rate_plan_repository.get(
                connection_history_summary.rate_plan_id
            )

            if get_rate_plan is not None:
                rate_plan_name = get_rate_plan.name
            else:
                rate_plan_name = None
        connection_history_summary.last_session = last_session
        connection_history_summary.rate_plan = rate_plan_name
        return connection_history_summary

    def _sim_usage_summary_total(
        slef, sim, sim_usage_analytics, carrier_name_list
    ) -> int:
        filtering_sim_data = filter(lambda x: x.imsi == sim.imsi, sim_usage_analytics)
        sim_usage_summary_total = sum(
            map(
                lambda sim_data: sum(
                    map(
                        lambda sim_usage_summary: (
                            sim_usage_summary.usage  # type: ignore
                        ),
                        filter(
                            lambda summary: summary.carrier  # type: ignore
                            in map(lambda x: x.carrier, carrier_name_list),
                            sim_data.usageSummary,
                        ),
                    )
                ),
                filtering_sim_data,
            )
        )
        return sim_usage_summary_total

    def _merge_sim_managment_with_usage(
        self, sim_list, sim_usage, sim_usage_analytics, carrier_name_list
    ) -> Iterator[model.SimUsage]:
        """Function for compare sim iccid and push usage"""
        usage_dict = {row.iccid: row.usage for row in sim_usage}

        for sim in sim_list:
            sim_usage_summary_total = self._sim_usage_summary_total(
                sim, sim_usage_analytics, carrier_name_list
            )
            usage = usage_dict.get(sim.iccid)
            if sim_usage_summary_total is not None:
                if usage is None:
                    usage = 0
                sim_with_usage = model.SimUsage(
                    sim_id=sim.sim_id,
                    iccid=sim.iccid,
                    msisdn=sim.msisdn,
                    imsi=sim.imsi,
                    type=sim.type,
                    allocation_reference=sim.allocation_reference,
                    allocation_date=sim.allocation_date,
                    sim_status=sim.sim_status,
                    usage=usage,
                    rate_plan=sim.rate_plan,
                    ee_usage=sim_usage_summary_total,
                    sim_profile=sim.sim_profile,
                    msisdn_factor=sim.msisdn_factor,
                )
                yield sim_with_usage
            else:
                yield sim

    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.SimUsage], int, dict]:
        carrier_name_data = "EE"
        carrier_name = self.sim_repository.get_carrier_name(
            carrier_name=carrier_name_data
        )
        carrier_name_list = list(carrier_name)

        (
            total,
            totalActiveSims,
            totalDeactivatedSims,
            totalPendingSims,
            totalReadyActivationSims,
        ) = self.sim_repository.get_sim_usage_count(
            account_id=account_id, searching=searching
        )  # Function Updated

        if total == 0:
            raise exceptions.NoSimFound()

        sim_cards = self.get_sim_cards(account_id=account_id)
        if any(sim_card.rate_plan_id is None for sim_card in sim_cards):
            raise exceptions.RatePlanNotFound("Rate Plan not found.")

        sim_data = self.sim_repository.get_sim_usage(
            account_id, ordering=ordering, searching=searching, pagination=pagination
        )
        sim_list = list(sim_data)
        imsi = [IMSI(sim.imsi) for sim in sim_list]
        today = datetime.date.today()
        month_ = Month(year=today.year, month=today.month, day=1)
        sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month_)
        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi,
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        sim_management: Iterator[model.SimUsage] = self._merge_sim_managment_with_usage(
            sim_list, sim_usage, sim_usage_analytics.summary, carrier_name_list
        )

        # calculates statistics
        imsi_list = self.sim_repository.get_sim_imsi_by_account_id(
            account_id=account_id,
            searching=searching,
        )

        total_usage = self.cdr_repository.get_sim_usage_total_by_imsi(
            imsi_list, month_  # type: ignore
        )

        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi_list,  # type: ignore
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        totalEEUsageData = 0
        for sim in sim_list:
            ee_usage = self._sim_usage_summary_total(
                sim, sim_usage_analytics.summary, carrier_name_list
            )
            totalEEUsageData = totalEEUsageData + ee_usage

        statistics_dict = {
            "totalUsage": total_usage,
            "totalEEUsageData": totalEEUsageData,
            "totalActiveSims": totalActiveSims,
            "totalReadyActivationSims": totalReadyActivationSims,
            "totalDeactivatedSims": totalDeactivatedSims,
            "totalPendingSims": totalPendingSims,
            "totalSims": total,
        }
        return sim_management, total, statistics_dict

    def get_sim_usage_export(
        self,
        account_id: int,
    ) -> Iterator[model.SimUsage]:
        carrier_name_data = "EE"
        carrier_name = self.sim_repository.get_carrier_name(
            carrier_name=carrier_name_data
        )
        carrier_name_list = list(carrier_name)

        sim_cards = self.get_sim_cards(account_id=account_id)
        if any(sim_card.rate_plan_id is None for sim_card in sim_cards):
            raise exceptions.RatePlanNotFound("Rate Plan not found.")

        sim_data = self.sim_repository.get_sim_usage(account_id)
        sim_list = list(sim_data)
        imsi = [IMSI(sim.imsi) for sim in sim_list]
        today = datetime.date.today()
        month_ = Month(year=today.year, month=today.month, day=1)
        sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month_)
        sim_usage_analytics_data = model.MarketShareModel(
            from_date=None,
            to_date=None,
            imsis=imsi,
        )
        sim_usage_analytics = self.market_share_api.get_imsis_usage(
            sim_usage_analytics_data
        )
        sim_management: Iterator[model.SimUsage] = self._merge_sim_managment_with_usage(
            sim_list, sim_usage, sim_usage_analytics.summary, carrier_name_list
        )
        return sim_management

    def _merge_sim_with_usage(
        self, active_sim_list, active_sims_usage
    ) -> Iterator[model.ActiveSimMonthlyStatistic]:
        """Function for compare sim iccid and push usage"""
        usage_dict = {row.iccid: row.usage for row in active_sims_usage}
        for sim in active_sim_list:
            usage = usage_dict.get(sim.iccid)
            if usage is not None:
                sim_with_usage = model.ActiveSimMonthlyStatistic(
                    id=sim.id,
                    imsi=sim.imsi,
                    iccid=sim.iccid,
                    msisdn=sim.msisdn,
                    sim_status=sim.sim_status,
                    is_first_activation=sim.is_first_activation,
                    usage=usage,
                    rate_plan_id=sim.rate_plan_id,
                )
                yield sim_with_usage
            else:
                yield sim

    def cards_active_statistic(
        self,
        account_id: int,
        month: Month,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.ActiveSimMonthlyStatistic], int]:
        """Function for get active sim,usage, and first activation for the month"""
        active_sims = self.sim_repository.cards_active_statistic(
            account_id=account_id, month=month, pagination=pagination
        )
        active_list = list(active_sims)
        imsi = [IMSI(sim.imsi) for sim in active_list]
        active_sim_usage = self.cdr_repository.get_sim_usage_by_imsi(imsi, month)

        sim_usage: Iterator[
            model.ActiveSimMonthlyStatistic
        ] = self._merge_sim_with_usage(active_list, active_sim_usage)

        total = self.sim_repository.cards_active_statistic_count(
            account_id=account_id, month=month
        )
        if total == 0:
            raise exceptions.NoActiveSimFound(account_id, month)
        return sim_usage, total

    def connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimCDRHistory]:

        connection_history = self.cdr_repository.connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimCDRHistory], int]:
        """Function for connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        logger.info(f"#####account_id:-{account_id}")
        if response:
            connection_history = self.cdr_repository.connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )

            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def get_month_usage(self, account_id: int | None = None) -> model.MonthUsage:
        iccid_list = [
            ICCID(sim_card.iccid)
            for sim_card in self.get_sim_cards(account_id=account_id)
        ]
        if len(iccid_list) == 0:
            raise exceptions.NoSimFound()
        total_usage = self.cdr_repository.usage_monthly_total(iccid=iccid_list) or 0
        total_sims = self.get_sim_count(account_id=account_id)
        total_active_sims = self.get_sim_count(account_id=account_id, active_only=True)
        total_deactivated_sims = self.get_sim_count(
            account_id=account_id, deactivated_only=True
        )
        total_pending_sims = self.get_sim_count(
            account_id=account_id, pending_only=True
        )
        total_ready_activation_sims = self.get_sim_count(
            account_id=account_id, ready_activation_only=True
        )
        result = model.MonthUsage(
            total_usage=total_usage,
            total_sims=total_sims,
            total_active_sims=total_active_sims,
            total_deactivated_sims=total_deactivated_sims,
            total_pending_sims=total_pending_sims,
            total_ready_activation_sims=total_ready_activation_sims,
        )
        return result

    def sim_status_details(
        self,
        sim_status: SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ) -> tuple[Iterator[model.SimStatusDetails], int]:
        total = self.sim_repository.sim_status_details_count(
            sim_status=sim_status,
            account_id=account_id,
        )
        logger.info(
            f"/cards/{{sim_status}} total sims with status {sim_status} : {total}"
        )
        if total == 0:
            raise exceptions.NoSimCardsStatus(sim_status)
        sim_response = self.sim_repository.sim_status_details(
            sim_status=sim_status,
            pagination=pagination,
            account_id=account_id,
        )
        converted_status = (
            model.SimStatusDetails(**item.__dict__) for item in sim_response
        )
        return converted_status, total

    def _get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
        except StopIteration:
            raise exceptions.SimCardsNotFound("Requested reference not found.")
        return sim_card

    def _get_sim_monthly_status(
        self, reference: IMSI, notification: SIMCardProviderAudit
    ):
        sim_card = self._get_sim_card(IMSI(reference))
        year = notification.audit_date.year
        month = notification.audit_date.month
        sim_status = SimStatus(notification.status)
        sim_monthly_status = SIMMonthlyStatus(
            sim_card_id=sim_card.id,
            month=Month(year=year, month=month, day=1),
            sim_status=sim_status,
            is_first_activation=True,
        )
        return sim_monthly_status

    def _add_sim_monthly_status(
        self, sim_monthly_status: SIMMonthlyStatus, sim_status: SimStatus
    ):
        is_sim_fee_applicable = self.sim_repository.check_sim_monthly_status(
            sim_monthly_status.sim_card_id, None
        )
        if is_sim_fee_applicable:
            if not self.sim_repository.check_sim_monthly_status(
                sim_monthly_status.sim_card_id, sim_monthly_status.month
            ):
                sim_monthly_status.is_first_activation = False
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)
            else:
                self.sim_repository.update_sim_monthly_status(
                    sim_monthly_status.sim_card_id,
                    sim_monthly_status.month,
                    SimStatus(sim_status),
                )
        else:
            if sim_status == model.SimStatus.ACTIVE:
                # New Billing logic while allocation we set flag true else false always
                sim_monthly_status.is_first_activation = False
                # New Billing logic while allocation we set flag true else false always
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)

    def _mapping_carrier_name(self, marketShareUageList, carrierName):
        carrier_mapping = {
            carrier_obj.carrier: carrier_obj.carrier_name for carrier_obj in carrierName
        }
        for item in marketShareUageList:
            carrier_code = item.carrier
            carrier_name = carrier_mapping.get(carrier_code)
            if carrier_name is not None:
                item.carrier = carrier_name
            else:
                item.carrier = carrier_code
        return marketShareUageList

    def _merge_carrier_usage(self, mapped_list):
        usage_totals = {}
        for item in mapped_list:
            carrier = item.carrier
            usage = item.usage
            if carrier in usage_totals:
                usage_totals[carrier] += usage
            else:
                usage_totals[carrier] = usage
        return [
            MarketShareUsage(carrier=key, usage=value)
            for key, value in usage_totals.items()
        ]

    def update_sim_card_based_on_response(
        self,
        sim_provider_log: SIMCardProviderAudit,
        provider_log,
        sim_status: SimStatus,
        given_status: NotificationStatus,
        given_request_type: RequestType,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        if (given_status == NotificationStatus.SUCCESS) and (
            provider_log.requestType.upper() == given_request_type.name
        ):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(sim_status)
            )
            sim_provider_log.status = SimStatus(sim_status)
            self._add_sim_monthly_status(
                self._get_sim_monthly_status(IMSI(provider_log.imsi), sim_provider_log),
                SimStatus(sim_status),
            )
        elif (
            given_status == NotificationStatus.FAILURE
            or given_status == NotificationStatus.FAILED
        ) and (provider_log.requestType.upper() == given_request_type.name):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(provider_log.priorStatus)
            )
            sim_card = self.get_sim_card(IMSI(provider_log.imsi))
            iccid = sim_card.iccid
            msisdn = sim_card.msisdn

            audit_detail = model.SIMCardAudit(
                uuid4(),
                IMSI(provider_log.imsi),
                ICCID(iccid),
                MSISDN(msisdn),
                given_request_type.name,
                SimStatus(sim_status),
                SimStatus(provider_log.priorStatus),
                field="Status",
                action="Updated",
                client_ip=client_ip,
                created_by=created_by,
                audit_date=datetime.datetime.today(),
            )
            self.audit_service.add_sim_audit_api([audit_detail])

        return True

    def update_sim_card_by_imsi(
        self,
        imsi: IMSI,
        client_ip: str,
        created_by: str | None = None,
    ) -> bool:
        """Function to get SIM work id status and update by IMSI"""

        provider_id = self.sim_repository.get_provider_id(imsi)
        if provider_id == 0 or provider_id is None:
            raise exceptions.WorkItemIdNotFound(f"No request found with imsi {imsi}")

        work_id = self.audit_service.get_sim_pending_info_audit_api(imsi)
        if work_id == 0 or work_id is None:
            raise exceptions.WorkItemIdNotFound(f"No request found with imsi {imsi}")

        ppl_response = self.provisioning.sim_workitem_status(work_id)
        provider_log = self.audit_service.get_sim_provider_log_audit_api(work_id)

        sim_status = {
            RequestType.CEASE: SimStatus.DEACTIVATED,
            RequestType.PROVIDE: SimStatus.ACTIVE,
        }.get(ppl_response.request_type)

        audit_provider_detail = model.SIMCardProviderAudit(
            activity_id=str(provider_log.activityId),
            sim_activity_log_uuid=UUID(provider_log.simActivityLogUuid),
            audit_date=ppl_response.audit_date,
            message=ppl_response.message,
            status=f"{sim_status}",
            work_id=work_id,
            prior_status=provider_log.status,
        )

        if ppl_response.status != NotificationStatus.SUCCESS:
            audit_provider_detail.status = ppl_response.status

        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        return self.update_sim_card_based_on_response(
            audit_provider_detail,
            provider_log,
            SimStatus(sim_status),
            ppl_response.status,
            ppl_response.request_type,
            client_ip,
            created_by,
        )

    def bulk_background_process(
        self,
        imsi_list: list[IMSI],
        created_by: str,
        sim_status: model.SimStatus,
        client_ip: str | None = None,
    ) -> None:
        for imsi in imsi_list:
            try:
                if sim_status == model.SimStatus.ACTIVE:
                    self.activate_sim(imsi, created_by, client_ip)
                else:
                    self.suspend_sim(imsi, created_by, client_ip)
            except exceptions.SimCardsNotFound as e:
                logger.critical(f"IMSI:{imsi}, message:{e}")
                continue
            except exceptions.SimActivationError as e:
                logger.critical(f"IMSI:{imsi}, activation:{e}")
                continue
            except exceptions.SimDeActivationError as e:
                logger.critical(f"IMSI:{imsi}, deactivation:{e}")
                continue
            except Exception as e:
                logger.error(f"IMSI:{imsi}, message:{e}")
                raise exceptions.BulkProcessingError("Could not process the request")

    def copy_monthly_statistics(self) -> int:
        copied_data = self.sim_repository.copy_monthly_statistics()
        total_rows_affected = self.sim_repository.add_bulk_sim_monthly_statistics(
            list(copied_data)
        )
        return total_rows_affected

    def get_imsis(
        self,
        iccids: list[ICCID],
    ) -> Iterator[model.IMSIDetails]:
        """Function for get SIM based on ICCID"""
        yield from self.sim_repository.get_imsis(iccids)

    def get_market_share_by_account(
        self, market_share_data: MarketShareData
    ) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)

        account_id = market_share_data.account_id
        result_data = self.sim_repository.get_market_share_by_account(account_id)
        result_list = list(result_data)
        if not result_list:
            raise NotFound(f"IMSI Not found with account id {account_id}")
        request_imsis = [IMSI(row.imsi) for row in result_list]
        request_data = MarketShareModel(
            imsis=request_imsis,
            from_date=market_share_data.from_date,
            to_date=market_share_data.to_date,
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        if not marketShareUsage.summary:
            raise NotFound("Response is received but no summary data found")
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        total_account_usage = sum(int(usage.usage) for usage in merged_list)
        marketShareUsage.summary = merged_list
        marketShareUsage.totalUsage = total_account_usage
        return marketShareUsage

    def get_market_share(self, period: MarketSharePeriod) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)
        result_data = self.sim_repository.get_market_share()
        result_list = list(result_data)
        if not result_list:
            raise NotFound("IMSI Not found with any account id")
        request_imsis = [IMSI(row.imsi) for row in result_list]
        request_data = MarketShareModel(
            imsis=request_imsis, from_date=period.from_date, to_date=period.to_date
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        marketShareUsage.summary = merged_list
        return marketShareUsage

    def voice_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:

        connection_history = self.cdr_repository.voice_connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimVoiceCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimVoiceCDRHistory], int]:
        """Function for voice connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        if response:
            connection_history = self.cdr_repository.voice_connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimVoiceCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.voice_connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )
            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SimSMSCDRHistory], int]:
        """Function for sms connection history"""

        response = self.check_imsi_account(account_id=account_id, imsi=[imsi])
        if response:
            connection_history = self.cdr_repository.sms_connection_history(
                imsi=imsi,
                month=month,
                pagination=pagination,
                searching=searching,
            )
            converted_history = (
                model.SimSMSCDRHistory(**item.__dict__) for item in connection_history
            )
            total = self.cdr_repository.sms_connection_history_count(
                imsi=imsi,
                month=month,
                searching=searching,
            )
            if total == 0:
                raise exceptions.NoConnectionHistory(imsi, month)
            return converted_history, total
        else:
            raise exceptions.IMSIDoesNotExit(imsi)

    def sms_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:

        connection_history = self.cdr_repository.sms_connection_history(
            imsi=imsi,
            month=month,
        )
        converted_history = (
            model.SimSMSCDRHistory(**item.__dict__) for item in connection_history
        )
        return converted_history

    def get_market_share_imsi(
        self, period: MarketSharePeriod, imsi: IMSI
    ) -> model.MarketShareCarrier:
        carrier_name = self.sim_repository.get_carrier_name()
        carrier_name_list = list(carrier_name)
        request_data = MarketShareModel(
            imsis=[imsi], from_date=period.from_date, to_date=period.to_date
        )
        marketShareUsage = self.market_share_api.market_share_usage(request_data)
        mapped_list = self._mapping_carrier_name(
            marketShareUsage.summary, carrier_name_list
        )
        merged_list = self._merge_carrier_usage(mapped_list)
        merged_list = sorted(merged_list, key=attrgetter("usage"), reverse=True)

        total_imsi_usage = sum(int(usage.usage) for usage in merged_list)
        marketShareUsage.summary = merged_list
        marketShareUsage.totalUsage = total_imsi_usage
        return marketShareUsage

    def custom_imsi_allocation(
        self,
        title: str,
        account_id: int,
        rate_plan_id: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        file_name: str,
        file: BinaryIO,
        client_ip: str,
        created_by: str,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
        file_parser=FileNameParser,
    ) -> model.AllocationResult:
        rate_plan = self.rate_plan_repository.get(rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{rate_plan_id} does not exist."
            )

        if rate_plan.account_id != account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan."
            )
        file_parser = FileNameParser()
        form_factor_code = file_parser._parse_file_name(file_name, file)
        imsi_list = parser_impl(file=file, delimiter=";")
        sorted_sim_imsi = list(sorted(filter(None, imsi_list)))  # type: ignore
        if not sorted_sim_imsi:
            raise exceptions.IMSINotAvailableForAllocation(
                f"IMSI not available for allocation,in file {file_name}"
            )

        total_sim, total_error_sim, error_list, valid_imsi = self.imsi_wise_error(
            imsi_list=sorted_sim_imsi, form_code=form_factor_code
        )

        rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
            id=rate_plan.id
        )
        valid_sim_count = len(valid_imsi)
        aggregated_sim_count = rate_plan_sim_count + valid_sim_count

        self._check_sim_limit(
            sim_count=aggregated_sim_count,
            rate_plan_sim_count=rate_plan_sim_count,
            sim_limit=rate_plan.sim_limit,
        )

        allocation_result = model.AllocationResult(
            totalSIM=total_sim,
            errorSIM=total_error_sim,
            results=error_list,
        )
        total_valid_sim = total_sim - total_error_sim
        allocation_details = model.AllocationDetails(
            file_name=file_name,
            total_sim=total_valid_sim,
            error_sim=total_error_sim,
        )

        available_msisdns = self.sim_repository.get_available_msisdn(
            valid_imsi, msisdn_factor
        )
        same_factor_allocation = False
        if len(available_msisdns) >= len(valid_imsi):
            same_factor_allocation = True

        free_msisdns = []
        if same_factor_allocation is False:
            valid_imsi_set = set(valid_imsi)
            free_msisdns = self.sim_repository.get_msisdn_factor(
                msisdn_factor=msisdn_factor, free_count=total_valid_sim
            )
            valid_imsi = valid_imsi[: len(free_msisdns)]

            # To get the current sim details
            current_sim_details = self.sim_repository.get_sim_cards(
                imsi_list=valid_imsi
            )
            current_sim_details_list = list(current_sim_details)
            sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in current_sim_details_list}
            logger.info(
                f"Total {len(valid_imsi)}/{len(sorted_sim_imsi)} "
                "eligible for allocation."
            )

        if valid_imsi:
            allocation_data = self.sim_repository.allocation_data(
                valid_imsi, account_id, rate_plan_id, title, created_by
            )
            allocation_list = list(allocation_data)

            self.sim_repository.add_allocation_list(
                allocation_list=allocation_list,
                allocation_details=allocation_details,
                imsi_list=valid_imsi,
            )

            self.sim_repository.bulk_update_msisdn_pool(
                imsis=valid_imsi,
                msisdns=free_msisdns,
                sim_profile=sim_profile,
                msisdn_factor=msisdn_factor,
                same_factor_allocation=same_factor_allocation,
            )

            allocated_imsi_list = [
                sim_card
                for sim_card in self.sim_repository.get_allocated_imsi(
                    imsi_list=valid_imsi
                )
            ]
            audit_details = []
            logger.info(f"Auditing {len(allocated_imsi_list)} records.")
            for reallocate in allocated_imsi_list:
                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Allocated",
                    prior_value="Warehouse",
                    new_value=str(account_id),
                    field="Account",
                    action="Allocated",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_details.append(audit_detail)

                if same_factor_allocation is False:
                    prior_value = sim_imsi_lookup.get(reallocate.imsi)  # type: ignore
                    audit_detail = model.SIMCardAudit(
                        uuid=uuid4(),
                        imsi=IMSI(reallocate.imsi),
                        iccid=ICCID(reallocate.iccid),
                        msisdn=MSISDN(reallocate.msisdn),
                        request_type="Update MSISDN",
                        prior_value=prior_value,
                        new_value=MSISDN(reallocate.msisdn),
                        field="msisdn",
                        action="Updated",
                        client_ip=client_ip,
                        created_by=EmailStr(created_by),
                    )
                    audit_details.append(audit_detail)
            self.audit_service.add_allocation_audit_api(audit_details)

            range_dict = Counter(allocation.range_id for allocation in allocation_list)
            for range, range_count in range_dict.items():
                self.sim_repository.update_ranges(range, range_count)

        if same_factor_allocation is False:
            error_sims = list(valid_imsi_set - set(valid_imsi))
            allocation_result.errorSIM = total_error_sim + len(error_sims)
            allocation_result.results.extend(  # type: ignore
                {"sim": imsi, "issue": "MSISDN Type not available."}
                for imsi in error_sims
            )
        return allocation_result

    def check_sim_type_available(
        self, form_factor
    ) -> Iterable[tuple[str, FormFactor, int]]:
        available_sim_type = list(self.get_sim_remains())
        form_factor_exist = [
            (item[1], item[2]) for item in available_sim_type if item[1] == form_factor
        ]
        if not form_factor_exist:
            raise exceptions.SimTypeNotAvailable(
                f"Sim type {form_factor} not available for allocation"
            )

        return available_sim_type

    def imsi_wise_error(self, imsi_list, form_code):
        invalid_imsi = list(
            filter(
                lambda imsi: not (IMSI.min_length <= len(imsi) <= IMSI.max_length),
                imsi_list,
            )
        )
        valid_imsi = list(
            filter(
                lambda imsi: IMSI.min_length <= len(imsi) <= IMSI.max_length, imsi_list
            )
        )
        imsi_counts = Counter(valid_imsi)
        duplicate_imsi = []
        for imsi, count in imsi_counts.items():
            duplicate_imsi.extend([imsi] * (count - 1))
        all_imsi_obj = list(self.sim_repository.get_all_imsi(imsi_list=valid_imsi))
        not_available_imsi = list(
            set(valid_imsi)
            - set(list(map(lambda custom_model: custom_model.imsi, all_imsi_obj)))
        )
        already_allocated_imsi = dict(
            map(
                lambda custom_model: (custom_model.imsi, custom_model.account_name),
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and custom_model.form_factor == form_code,
                    all_imsi_obj,
                ),
            )
        )

        not_sim_type = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.form_factor != form_code,
                    all_imsi_obj,
                ),
            )
        )
        sim_ready_for_allocation = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is None
                    and custom_model.form_factor == form_code,
                    all_imsi_obj,
                ),
            )
        )
        combined_list = []
        if invalid_imsi:
            for sim in invalid_imsi:
                combined_list.append({"sim": sim, "issue": "Invalid IMSI format"})
        if not_available_imsi:
            for sim in not_available_imsi:
                combined_list.append({"sim": sim, "issue": "Not found in IMSI Ranges"})
        if already_allocated_imsi:
            for sim, account_name in already_allocated_imsi.items():
                combined_list.append(
                    {
                        "sim": sim,
                        "issue": f"Already allocated to {account_name} Account",
                    }
                )
        if not_sim_type:
            for sim in not_sim_type:
                combined_list.append(
                    {
                        "sim": sim,
                        "issue": f"IMSI does not belong {form_code} sim type",
                    }
                )
        if duplicate_imsi:
            for sim in duplicate_imsi:
                combined_list.append(
                    {
                        "sim": sim,
                        "issue": "Duplicate IMSI",
                    }
                )
        total_sim = len(imsi_list)
        error_sim = len(combined_list)
        return total_sim, error_sim, combined_list, list(sim_ready_for_allocation)

    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> bool:
        response = self.sim_repository.check_imsi_account(
            account_id=account_id, imsi=imsi
        )
        if response:
            if (account_id is None) or (account_id == response):
                return True
            else:
                return False
        else:
            return False

    def re_allocation_validation(
        self,
        imsi_list: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> tuple[model.ReAllocationResult, list[int]]:
        rate_plan = self.rate_plan_repository.get(rate_plan_id)

        if not rate_plan:
            raise exceptions.RatePlanNotFound(
                f"Rate Plan with id:{rate_plan_id} does not exist."
            )
        if rate_plan.account_id != account_id:
            raise exceptions.RatePlanAccountMappingError(
                "Incorrect account for the specified Rate Plan."
            )

        if len(imsi_list) != len(set(imsi_list)):
            raise exceptions.AllocationError("Duplicate IMSI selected")

        allocated_imsi_list = [
            sim_card
            for sim_card in self.sim_repository.get_allocated_imsi(imsi_list=imsi_list)
        ]
        account_ids_list = [allocation.account_id for allocation in allocated_imsi_list]
        if len(account_ids_list) != len(imsi_list):
            raise exceptions.IMSINotAvailableForAllocation(
                "IMSI not available for re_allocation"
            )
        if len(set(account_ids_list)) > 1:
            raise exceptions.IMSINotBelongToSameAccount(
                "IMSI should not belong to same account"
            )

        valid_imsis = self._get_valid_reallocation_imsi(
            sim_cards=allocated_imsi_list,
            rate_plan_id=rate_plan_id,
            account_id=account_id,
        )
        if valid_imsis.validSIM:
            rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
                id=rate_plan.id
            )
            aggregated_sim_count = rate_plan_sim_count + valid_imsis.validSIM

            self._check_sim_limit(
                sim_count=aggregated_sim_count,
                rate_plan_sim_count=rate_plan_sim_count,
                sim_limit=rate_plan.sim_limit,
            )
        rp_ids = [rp_id.rate_plan_id for rp_id in allocated_imsi_list]
        return valid_imsis, rp_ids  # type: ignore

    def _get_valid_reallocation_imsi(
        self, sim_cards, rate_plan_id: int, account_id: int
    ) -> model.ReAllocationResult:

        valid_reallocation_imsi_list = []
        same_account_valid_imsi_list = []
        for sim_card in sim_cards:
            sim_card_imsi = sim_card.imsi
            if sim_card.rate_plan_id != rate_plan_id:
                valid_reallocation_imsi_list.append(sim_card_imsi)
            if sim_card.account_id == account_id:
                same_account_valid_imsi_list.append(sim_card_imsi)

        validSIM = len(valid_reallocation_imsi_list)
        errorSIM = len(sim_cards) - validSIM

        message = (
            f"{validSIM} IMSIs will be re-allocated and"
            f" {errorSIM} IMSIs already assigned to choosen Rate Plan."
        )
        logger.debug(message)

        if not valid_reallocation_imsi_list:
            raise exceptions.ReAllocationError(message)
        return model.ReAllocationResult(
            validSIM=validSIM,
            errorSIM=errorSIM,
            message=message,
            valid_imsi_list=valid_reallocation_imsi_list,
            same_account_imsi_list=same_account_valid_imsi_list,
        )

    def re_allocation(
        self,
        account_id: int,
        rate_plan_id: int,
        imsi_list: list[IMSI],
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> bool:
        logger.debug(
            f"Re_allocation_Re-allocation of IMSI {imsi_list} "
            f"for account {account_id} and rate plan {rate_plan_id}"
        )

        reallocated_imsi_list = [
            sim_card
            for sim_card in self.sim_repository.get_allocated_imsi(imsi_list=imsi_list)
        ]
        audit_details = []

        for reallocate in reallocated_imsi_list:
            imsi, iccid, msisdn = reallocate.imsi, reallocate.iccid, reallocate.msisdn
            # Create audit details based on account or rate plan changes
            if reallocate.imsi in (same_account_imsi_list or []):
                audit_details.append(
                    model.SIMCardAudit(
                        uuid=uuid4(),
                        imsi=IMSI(imsi),
                        iccid=ICCID(iccid),
                        msisdn=MSISDN(msisdn),
                        request_type="Updated",
                        prior_value=str(reallocate.rate_plan_id),
                        new_value=str(rate_plan_id),
                        field="Rate Plan",
                        action="Updated",
                        client_ip=client_ip,
                        created_by=EmailStr(created_by),
                    )
                )
            else:
                audit_details.extend(
                    [
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(imsi),
                            iccid=ICCID(iccid),
                            msisdn=MSISDN(msisdn),
                            request_type="Re-allocated",
                            prior_value=str(reallocate.account_id),
                            new_value=str(account_id),
                            field="Account",
                            action="Re-allocated",
                            client_ip=client_ip,
                            created_by=EmailStr(created_by),
                        ),
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(imsi),
                            iccid=ICCID(iccid),
                            msisdn=MSISDN(msisdn),
                            request_type="Re-allocated",
                            prior_value=str(reallocate.rate_plan_id),
                            new_value=str(rate_plan_id),
                            field="Rate Plan",
                            action="Re-allocated",
                            client_ip=client_ip,
                            created_by=EmailStr(created_by),
                        ),
                    ]
                )

        self.audit_service.add_allocation_audit_api(audit_details)
        try:

            response = self.sim_repository.imsi_reallocation_func(
                account_id=account_id,
                rate_plan_id=rate_plan_id,
                imsis=imsi_list,
            )

            if response != "Success":
                self._roll_back_reallocation_audit(
                    rate_plan_id=rate_plan_id,
                    reallocated_imsi_list=reallocated_imsi_list,
                    account_id=account_id,
                    client_ip=client_ip,
                    created_by=created_by,
                    same_account_imsi_list=same_account_imsi_list,
                )
                logger.error(f"Re-alloction IMSI error - {str(response)}")
        except IntegrityError as e:
            logger.error(f"Imsi reallocation error.: {str(e)}")
            self._roll_back_reallocation_audit(
                rate_plan_id=rate_plan_id,
                reallocated_imsi_list=reallocated_imsi_list,
                account_id=account_id,
                client_ip=client_ip,
                created_by=created_by,
                same_account_imsi_list=same_account_imsi_list,
            )
            raise exceptions.IMSIWiseError(f"Imsi reallocation error.: {str(e)}")
        return True

    def _roll_back_reallocation_audit(
        self,
        rate_plan_id: int,
        reallocated_imsi_list: list[model.Allocation],
        account_id: int,
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> None:
        audit_data = []
        for reallocate in reallocated_imsi_list:
            if reallocate.imsi in same_account_imsi_list:  # type: ignore
                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.rate_plan_id),
                    new_value=str(rate_plan_id),
                    field="Rate Plan",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(audit_detail)
            else:
                audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.account_id),
                    new_value=str(account_id),
                    field="Account",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(audit_detail)

                rate_plan_audit_detail = model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(reallocate.imsi),
                    iccid=ICCID(reallocate.iccid),
                    msisdn=MSISDN(reallocate.msisdn),
                    request_type="Re-allocated",
                    prior_value=str(reallocate.rate_plan_id),
                    new_value=str(rate_plan_id),
                    field="Rate Plan",
                    action="Failed",
                    client_ip=client_ip,
                    created_by=EmailStr(created_by),
                )
                audit_data.append(rate_plan_audit_detail)

        self.audit_service.add_allocation_audit_api(audit_data)

    def update_msisdn(self, excel_data) -> bool:
        for _, row in excel_data.iterrows():
            imsi = row["IMSI"]
            msisdn = row["MSISDN"]
            if self.sim_repository.is_msisdn_already_exists(msisdn):
                raise exceptions.MSISDNExitError(
                    "MSISDN is already assigned to another SIM card."
                )
            if self.sim_repository.is_imsi_exists(imsi):
                raise exceptions.NoSimFound()
            self.sim_repository.update_sim_msisdn_by_imsi(imsi, msisdn_type(msisdn))
        return True

    def rate_plan_change_sim_validation(
        self,
        imsi: IMSI,
        account_id: int,
        rate_plan_id: int,
    ) -> model.RatePlanChangeSimLimitResult:

        try:
            new_rate_plan_info = (
                self.sim_repository.get_rate_plan_with_allocation_count(rate_plan_id)
            )

            if not new_rate_plan_info:
                raise exceptions.RatePlanNotFound(
                    f"Rate Plan with id: {rate_plan_id} does not exist."
                )
            if new_rate_plan_info.account_id != account_id:
                raise exceptions.RatePlanAccountMappingError(
                    "Incorrect account for the specified Rate Plan."
                )

            imsi_rate_plan_id = self.sim_repository.get_rate_plan_by_imsi(imsi=imsi)

            if not imsi_rate_plan_id:
                raise exceptions.SimCardsNotFound("SIM not found.")

            if imsi_rate_plan_id == rate_plan_id:
                raise exceptions.RatePlanException(
                    "IMSI already assigned to the specified plan."
                )

            if (
                new_rate_plan_info.sim_limit is None
                or new_rate_plan_info.allocated_sim_count < new_rate_plan_info.sim_limit
            ):
                message = "Rate Plan will be changed"
            else:
                raise exceptions.RatePlanChangeNotAllowed("Rate Plan can't be changed")

            return model.RatePlanChangeSimLimitResult(message=message)
        except Exception:
            raise

    def get_msisdn_factor(
        self,
        msisdn_factor: model.MSISDNFactor,
    ) -> str:
        """Function to get Random MSISDN based on SIM Profile"""
        msisdn = self.sim_repository.get_msisdn_factor(msisdn_factor)
        if not msisdn:
            raise exceptions.NotFound(
                f"No free MSISDN found with factor {msisdn_factor}"
            )
        return msisdn[0]

    def _validate_sim_card_details_by_imsi(
        self,
        existing_msisdn: MSISDN,
        existing_imsi: IMSI,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        existing_profile: model.SimProfile,
    ) -> None:
        if not existing_msisdn:
            logger.error(f"MSISDN Not found: {existing_msisdn}")
            raise exceptions.MSISDNNotFound("MSISDN not found.")

        if (
            imsi == existing_imsi
            and msisdn == existing_msisdn
            and sim_profile != existing_profile
        ):
            pass
        else:
            if existing_imsi:
                logger.error(
                    f"MSISDN is already associated with an IMSI: {existing_imsi}, "
                    f"MSISDN: {existing_msisdn}"
                )
                raise exceptions.AlreadyExist("Invalid MSISDN")

    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: msisdn_type,
        sim_profile: model.SimProfile,
        client_ip: str,
        created_by: str | None = None,
    ) -> model.UpdateSimCardDetailsResult:
        """Function to update MSISDN and right after need to perform deactivate"""
        sim_card = self.get_sim_card(IMSI(imsi))
        if SimStatus(sim_card.sim_status) and (
            SimStatus(sim_card.sim_status) == SimStatus.PENDING
            or SimStatus(sim_card.sim_status) == SimStatus.ACTIVE
        ):
            logger.error(
                f"Cannot update Pending or Active State Sim. "
                f"Imsi: {imsi}, msisdn: {msisdn}, "
                f"SimStatus: {sim_card.sim_status}"
            )
            raise exceptions.IMSIError("Cannot update Pending or Active Sim.")

        (
            existing_msisdn,
            existing_allocation_id,
            existing_imsi,
            existing_profile,
            existing_imsi_msisdn,
        ) = self.sim_repository.validate_sim_card_details_by_imsi(
            imsi=imsi, msisdn=msisdn
        )

        self._validate_sim_card_details_by_imsi(
            existing_msisdn=existing_msisdn,
            existing_imsi=existing_imsi,
            existing_profile=existing_profile,
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
        )

        response = self.sim_repository.update_sim_card_details_by_imsi(
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
            allocation_id=existing_allocation_id,
            existing_msisdn=existing_imsi_msisdn,
            existing_profile=existing_profile,
        )
        if not response:
            logger.error(
                f"Unexpected error occoured: {imsi}, msisdn: {msisdn}, "
                f"SimStatus: {sim_card.sim_status}"
                f"Response: {response}"
            )
            raise exceptions.IMSIError("Unexpected error occoured.")
        # Adding audit details
        audit_detail = []
        if sim_card.msisdn != msisdn:
            audit_detail.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(sim_card.iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Update MSISDN",
                    prior_value=MSISDN(sim_card.msisdn),
                    new_value=MSISDN(msisdn),
                    field="MSISDN",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )

        if existing_profile != sim_profile:
            audit_detail.append(
                model.SIMCardAudit(
                    uuid=uuid4(),
                    imsi=IMSI(imsi),
                    iccid=ICCID(sim_card.iccid),
                    msisdn=MSISDN(msisdn),
                    request_type="Update MSISDN",
                    prior_value=existing_profile,
                    new_value=sim_profile,
                    field="SIM Profile",
                    action="Updated",
                    client_ip=client_ip,
                    created_by=created_by,
                )
            )

        self.audit_service.add_upload_msisdn_audit_api(audit_details=audit_detail)

        sim_card = self.get_sim_card(IMSI(imsi))

        return model.UpdateSimCardDetailsResult(
            imsi=sim_card.imsi,
            msisdn=sim_card.msisdn,
            sim_profile=sim_profile,
        )

    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.MsisdnDetails], int]:
        total_count = self.sim_repository.pool_msisdn_count(searching=searching)
        if total_count == 0:
            raise exceptions.NotFound()
        response = list(
            self.sim_repository.get_msisdn_pool_details(
                pagination=pagination, searching=searching
            )
        )

        for upload_msisdn in response:
            upload_msisdn.logo_url = self._get_logo_url(upload_msisdn.logo_key)
        return response, total_count

    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        response = self.sim_repository.get_msisdn_export(searching=searching)
        if not response:
            raise exceptions.NotFound("No msisdn details found.")
        return response

    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        return self.sim_repository.get_available_msisdn_count()

    def creat_msisdn_response(
        self,
        invalid_format: list[str],
        pool_msisdn: list[MSISDN],
        sim_card_msisdn: list[MSISDN],
        duplicate_msisdn: list[MSISDN],
    ) -> model.MsisdnResult:

        results = []

        # Add invalid format errors
        for msisdn in invalid_format:
            results.append({"msisdn": msisdn, "issue": "Invalid MSISDN format"})

        for msisdn in duplicate_msisdn:
            results.append({"msisdn": msisdn, "issue": "Duplicate MSISDN"})

        for msisdn in pool_msisdn:
            results.append({"msisdn": msisdn, "issue": "Already uploaded"})

        # Add already allocated errors
        for msisdn in sim_card_msisdn:
            results.append({"msisdn": msisdn, "issue": "Already uploaded"})

        return model.MsisdnResult(
            error_msisdn=len(results),
            error_result=results if results else [],
        )

    def upload_msisdn(
        self,
        msisdn_list: list[MSISDN],
        invalid_format: list[str],
        duplicate_msisdn: list[MSISDN],
        client_ip: str,
        uploaded_by: str | None = None,
    ) -> model.MsisdnResult:
        """
        MSISDN Factor will be set to NATIONAL default
        NATIONAL if msisdn starts with 447 or any other number
        INTERNATIONAL if msisdn starts with 883
        """

        pool_msisdns = set(
            self.sim_repository.get_msisdn_in_pool_msisdn_table(msisdn_list=msisdn_list)
        )
        logger.info(f"Pool MSISDNs -{pool_msisdns}")
        remaining_msisdn = set(msisdn_list) - pool_msisdns
        sim_card_msisdn = set(
            self.sim_repository.get_msisdn_in_sim_card_table(
                msisdn_list=remaining_msisdn
            )
        )
        logger.info(f"Sim card MSISDNs -{sim_card_msisdn}")
        if remaining_msisdn:
            msisdn_pool = []
            audit_details = []
            created_at = datetime.datetime.today()
            for msisdn in remaining_msisdn:
                # Adding msisdn_pool details
                msisdn_factor = (
                    MSISDNFactor.INTERNATIONAL
                    if msisdn.startswith("883")
                    else MSISDNFactor.NATIONAL
                )
                pool_msisdn = model.MsisdnPool(
                    msisdn=msisdn,
                    sim_profile=model.SimProfile.DATA_ONLY,
                    msisdn_factor=msisdn_factor,
                    uploaded_by=uploaded_by,
                    created_at=created_at,
                )
                msisdn_pool.append(pool_msisdn)

                # Adding audit details
                audit_details.extend(
                    [
                        model.MsisdnPoolAudit(
                            uuid=uuid4(),
                            msisdn=MSISDN(msisdn),
                            request_type="Upload MSISDN",
                            prior_value="Warehouse",
                            new_value=MSISDN(msisdn),
                            field="MSISDN",
                            action="Uploaded",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        ),
                        # model.MsisdnPoolAudit(
                        #     uuid=uuid4(),
                        #     msisdn=MSISDN(msisdn),
                        #     request_type="Upload MSISDN",
                        #     prior_value="Warehouse",
                        #     new_value=model.SimProfile.DATA_ONLY.name,
                        #     field="SIM Profile",
                        #     action="Uploaded",
                        #     client_ip=client_ip,
                        #     created_by=uploaded_by,
                        # ),
                        # model.MsisdnPoolAudit(
                        #     uuid=uuid4(),
                        #     msisdn=MSISDN(msisdn),
                        #     request_type="Upload MSISDN",
                        #     prior_value="Warehouse",
                        #     new_value=msisdn_factor,
                        #     field="MSISDN Type",
                        #     action="Uploaded",
                        #     client_ip=client_ip,
                        #     created_by=uploaded_by,
                        # ),
                    ]
                )
            self.audit_service.add_upload_msisdn_audit_api(audit_details=audit_details)
            self.sim_repository.upload_msisdn(msisdn_pool)
        return self.creat_msisdn_response(
            invalid_format=invalid_format,
            pool_msisdn=list(pool_msisdns),
            sim_card_msisdn=list(sim_card_msisdn),
            duplicate_msisdn=duplicate_msisdn,
        )

    def _validate_sim_card_details_by_imsi_msisdn(
        self,
        current_sim_details: list[model.SIMCard],
        valid_imsi_list: list[IMSI],
        duplicate_imsi: list[IMSI],
        valid_data: DataFrame,
        valid_msisdn_list: list[MSISDN],
        duplicate_msisdn: list[MSISDN],
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
    ):
        valid_data_details = valid_data.to_dict(orient="records")
        valid_data_look_up = {sim["IMSI"]: sim["MSISDN"] for sim in valid_data_details}
        valid_data_msisdn_look_up = {
            sim["MSISDN"]: sim["IMSI"] for sim in valid_data_details
        }
        not_available_imsi = list(
            set(valid_imsi_list)
            - set(
                list(map(lambda custom_model: custom_model.imsi, current_sim_details))
            )
        )

        unallocated_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is None,
                    current_sim_details,
                ),
            )
        )

        pending_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and SimStatus(custom_model.sim_status) == SimStatus.PENDING,
                    current_sim_details,
                ),
            )
        )

        active_imsi = list(
            map(
                lambda custom_model: custom_model.imsi,
                filter(
                    lambda custom_model: custom_model.allocation_id is not None
                    and SimStatus(custom_model.sim_status) == SimStatus.ACTIVE,
                    current_sim_details,
                ),
            )
        )

        updated_valid_imsi_list = list(
            set(valid_imsi_list)
            - set(duplicate_imsi)
            - set(unallocated_imsi)
            - set(pending_imsi)
            - set(active_imsi)
            - set(not_available_imsi)
        )
        updated_valid_msisdn_list = list(set(valid_msisdn_list) - set(duplicate_msisdn))
        # Filter data where IMSI is in imsi_list
        filtered_data = valid_data[valid_data["IMSI"].isin(updated_valid_imsi_list)]

        # Further filter data where MSISDN is in msisdn_list
        filtered_data = filtered_data[
            filtered_data["MSISDN"].isin(updated_valid_msisdn_list)
        ]

        # Store the final filtered records
        new_updated_records = filtered_data.to_dict(orient="records")

        valid_sim_card_details = self.sim_repository.validate_bulk_sim_card_details(
            sim_card_list=new_updated_records,
            valid_imsi_list=updated_valid_imsi_list,
            valid_msisdn_list=updated_valid_msisdn_list,
        )

        msisdn_not_found = []
        msisdn_associated_with_an_imsi = []
        msisdn_msisdn_factor_mismatch = []

        already_associated_imsi_with_msisdn = []
        for sim_details in valid_sim_card_details:
            if not sim_details.msisdn_value:
                logger.error(f"MSISDN Not found: {sim_details.requested_msisdn}")
                msisdn_not_found.append(sim_details.requested_msisdn)

            if sim_details.msisdn_value and sim_details.msisdn_factor != msisdn_factor:
                logger.error(
                    f"MSISDN Factor Mismatch: "
                    f"Existing={sim_details.msisdn_value}, "
                    f"Expected={sim_details.msisdn_factor}"
                )
                msisdn_msisdn_factor_mismatch.append(sim_details.msisdn_value)

            if (
                sim_details.existing_msisdn
                and sim_details.existing_msisdn == sim_details.requested_msisdn
                and sim_details.msisdn_sim_profile == sim_profile
                and sim_details.existing_msisdn not in msisdn_msisdn_factor_mismatch
            ):
                logger.error(
                    f"SIM is already associated with requested MSISDN: "
                    f"IMSI={sim_details.requested_imsi}, "
                    f"MSISDN={sim_details.requested_msisdn}"
                )
                already_associated_imsi_with_msisdn.append(sim_details.requested_imsi)

            if (
                sim_details.existing_imsi
                and sim_details.existing_msisdn
                and sim_details.existing_msisdn != sim_details.requested_msisdn
                and sim_details.requested_msisdn not in msisdn_msisdn_factor_mismatch
                and sim_details.requested_msisdn
                not in already_associated_imsi_with_msisdn
            ):
                logger.error(
                    f"MSISDN is already associated with an "
                    f"IMSI: {sim_details.existing_imsi}, "
                    f"MSISDN: {sim_details.requested_msisdn}"
                )
                msisdn_associated_with_an_imsi.append(sim_details.requested_msisdn)

            logger.info(
                f"Allocation ID: {sim_details.allocation_id}, "
                f"Existing MSISDN: {sim_details.existing_msisdn}"
            )

        final_valid_imsi_list = list(
            set(updated_valid_imsi_list) - set(already_associated_imsi_with_msisdn)
        )
        final_valid_msisdn_list = list(
            set(updated_valid_msisdn_list)
            - set(msisdn_not_found)
            - set(msisdn_associated_with_an_imsi)
            - set(msisdn_msisdn_factor_mismatch)
        )

        # Filter data where IMSI is in imsi_list
        final_filtered_data = valid_data[valid_data["IMSI"].isin(final_valid_imsi_list)]

        # Further filter data where MSISDN is in msisdn_list
        final_filtered_data = final_filtered_data[
            final_filtered_data["MSISDN"].isin(final_valid_msisdn_list)
        ]

        # Store the final filtered records
        final_updated_records = final_filtered_data.to_dict(orient="records")

        final_valid_sim_card_details = (
            self.sim_repository.validate_bulk_sim_card_details(
                sim_card_list=final_updated_records,
                valid_imsi_list=final_valid_imsi_list,
                valid_msisdn_list=final_valid_msisdn_list,
            )
        )

        if not final_valid_sim_card_details:
            final_valid_imsi_list = []

        error_response = []
        if not_available_imsi:
            for imsi in not_available_imsi:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi, None),
                        "imsi": imsi,
                        "issue": "IMSI not found",
                    }
                )

        if duplicate_imsi:
            for imsi in duplicate_imsi:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi, None),
                        "imsi": imsi,
                        "issue": "Duplicate IMSI found",
                    }
                )

        if unallocated_imsi:
            for imsi in unallocated_imsi:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi, None),
                        "imsi": imsi,
                        "issue": "IMSI allocation not found",
                    }
                )

        if pending_imsi:
            for imsi in pending_imsi:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi, None),
                        "imsi": imsi,
                        "issue": "The SIM cannot be updated while it is pending",
                    }
                )

        if active_imsi:
            for imsi in active_imsi:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi, None),
                        "imsi": imsi,
                        "issue": "The SIM cannot be updated while it is active",
                    }
                )

        if already_associated_imsi_with_msisdn:
            for imsi_value in already_associated_imsi_with_msisdn:
                error_response.append(
                    {
                        "msisdn": valid_data_look_up.get(imsi_value, None),
                        "imsi": imsi_value,
                        "issue": "Invalid IMSI and MSISDN",
                    }
                )

        if duplicate_msisdn:
            for msisdn in duplicate_msisdn:
                error_response.append(
                    {
                        "msisdn": msisdn,
                        "imsi": valid_data_msisdn_look_up.get(msisdn, None),
                        "issue": "Duplicate MSISDN found",
                    }
                )

        if msisdn_not_found:
            for msisdn in msisdn_not_found:
                error_response.append(
                    {
                        "msisdn": msisdn,
                        "imsi": valid_data_msisdn_look_up.get(msisdn, None),
                        "issue": "MSISDN Not found",
                    }
                )

        if msisdn_associated_with_an_imsi:
            for msisdn in msisdn_associated_with_an_imsi:
                error_response.append(
                    {
                        "msisdn": msisdn,
                        "imsi": valid_data_msisdn_look_up.get(msisdn, None),
                        "issue": "Invalid MSISDN",
                    }
                )

        if msisdn_msisdn_factor_mismatch:
            for msisdn in msisdn_msisdn_factor_mismatch:
                error_response.append(
                    {
                        "msisdn": msisdn,
                        "imsi": valid_data_msisdn_look_up.get(msisdn, None),
                        "issue": "MSISDN Type Mismatch",
                    }
                )
        if invalid_records:
            for invalid_data in invalid_records:

                error_response.append(
                    {
                        "msisdn": invalid_data.get("MSISDN"),
                        "imsi": invalid_data.get("IMSI"),
                        "issue": "Invalid records",
                    }
                )

        return final_valid_sim_card_details, final_valid_imsi_list, error_response

    def bulk_update_sim_card_details(
        self,
        total_records: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
        duplicate_imsi: list[IMSI],
        duplicate_msisdn: list[MSISDN],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[MSISDN],
        client_ip: str,
        valid_data: DataFrame,
        uploaded_by: str | None = None,
    ) -> model.BulkSimCardUpdateResult:

        current_sim_details = self.sim_repository.get_sim_cards(
            imsi_list=valid_imsi_list
        )
        current_sim_details_list = list(current_sim_details)
        sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in current_sim_details_list}
        sim_profile_lookup = {
            sim.imsi: sim.sim_profile for sim in current_sim_details_list
        }
        (
            final_valid_results,
            final_valid_imsi_list,
            error_response,
        ) = self._validate_sim_card_details_by_imsi_msisdn(
            current_sim_details=current_sim_details_list,
            valid_imsi_list=valid_imsi_list,
            duplicate_imsi=duplicate_imsi,
            valid_data=valid_data,
            valid_msisdn_list=valid_msisdn_list,
            duplicate_msisdn=duplicate_msisdn,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_records,
        )

        response = self.sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=final_valid_results, sim_profile=sim_profile
        )

        if not response:
            logger.error(f"Response: {response}")
            raise exceptions.IMSIError("Unexpected error occoured.")

        final_valid_reponse = self.sim_repository.get_sim_cards(
            imsi_list=final_valid_imsi_list
        )

        audit_details = []
        if final_valid_imsi_list:
            for sim_details in final_valid_reponse:
                prior_profile = sim_profile_lookup.get(sim_details.imsi, None)
                prior_msisdn = sim_imsi_lookup.get(sim_details.imsi, None)
                # Adding audit details
                if prior_msisdn != sim_details.msisdn:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_details.imsi),
                            iccid=ICCID(sim_details.iccid),
                            msisdn=MSISDN(sim_details.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_msisdn,
                            new_value=MSISDN(sim_details.msisdn),
                            field="MSISDN",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )
                if (
                    sim_details.sim_profile
                    and prior_profile != sim_details.sim_profile.name
                ):
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_details.imsi),
                            iccid=ICCID(sim_details.iccid),
                            msisdn=MSISDN(sim_details.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_profile,
                            new_value=sim_details.sim_profile,
                            field="SIM Profile",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )

            self.audit_service.add_upload_msisdn_audit_api(audit_details=audit_details)

        return model.BulkSimCardUpdateResult(
            total_details=total_records,
            error_details=len(error_response),
            error_results=error_response,
        )

    @staticmethod
    def get_common_value(data: dict) -> str | None:
        if not data:
            raise ValueError("The dictionary is empty.")

        first_value = next(iter(data.values()))

        if not all(value == first_value for value in data.values()):
            return None

        return first_value

    def validate_common_request(self, msisdn_list: list[msisdn_type]) -> bool:
        all_same_type = self.sim_repository.validate_msisdn_update_request(
            msisdn_list, "msisdn_factor"
        )
        all_same_profile = self.sim_repository.validate_msisdn_update_request(
            msisdn_list, "sim_profile"
        )
        if (not all_same_type) or (not all_same_profile):
            raise exceptions.IMSIError(
                "The requested MSISDNs have different MSISDN Type or "
                "belong to different SIM Profile."
            )
        return True

    def update_sim_card_details(
        self,
        sim_profile: model.SimProfile,
        total_records: int,
        client_ip: str,
        msisdn_factor: model.MSISDNFactor,
        uploaded_by: str | None = None,
        imsi_list: list[IMSI] = [],
        msisdn_list: list[msisdn_type] = [],
        duplicate_imsi: list[IMSI] = [],
        duplicate_msisdn: list[MSISDN] = [],
        valid_data: list[dict] = [],
        invalid_data: list[dict] = [],
    ):
        msisdn_list = [msisdn_type(data.get("MSISDN")) for data in valid_data]
        self.validate_common_request(msisdn_list)
        sim_details = list(self.sim_repository.get_sim_cards(imsi_list=imsi_list))
        sim_imsi_lookup = {sim.imsi: sim.msisdn for sim in sim_details}
        sim_profile_lookup = {sim.imsi: sim.sim_profile for sim in sim_details}
        sim_cards = self.sim_repository.validate_bulk_sim_card_details(
            valid_data, imsi_list, msisdn_list  # type: ignore
        )
        msisdn_factor_lookup = {
            sim.requested_imsi: sim.msisdn_factor for sim in sim_cards
        }
        common_msisdn_factor = self.get_common_value(msisdn_factor_lookup)
        imsi_list_set = set(imsi_list)
        if (
            (common_msisdn_factor is not None)
            and (common_msisdn_factor != msisdn_factor)
        ) or (common_msisdn_factor is None):
            free_msisdn_list = self.sim_repository.get_msisdn_factor(
                msisdn_factor, len(imsi_list)
            )
            temp_imsi_list = imsi_list[:]
            for sim in sim_details:
                if (sim.allocation_id is not None) and (
                    SimStatus(sim.sim_status) == (SimStatus.ACTIVE or SimStatus.PENDING)
                ):
                    temp_imsi_list.remove(sim.imsi)
            valid_data = [
                {**record, "MSISDN": msisdn}
                for record, msisdn in zip(valid_data, free_msisdn_list)
                if not temp_imsi_list.remove(record["IMSI"])  # type: ignore
            ]
            msisdn_list = free_msisdn_list
            imsi_list = list(imsi_list_set - set(temp_imsi_list))
        if not imsi_list:
            raise exceptions.MSISDNNotFound("No free MSISDNs found.")
        (
            final_valid_results,
            final_valid_imsi_list,
            error_response,
        ) = self._validate_sim_card_details_by_imsi_msisdn(
            current_sim_details=sim_details,
            valid_imsi_list=imsi_list,
            duplicate_imsi=duplicate_imsi,
            valid_data=DataFrame(valid_data),
            valid_msisdn_list=msisdn_list,  # type: ignore
            duplicate_msisdn=duplicate_msisdn,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_data,
        )

        response = self.sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=final_valid_results, sim_profile=sim_profile
        )

        if not response:
            logger.error(f"Response: {response}")
            raise exceptions.IMSIError("Unexpected error occoured.")

        final_valid_reponse = list(
            self.sim_repository.get_sim_cards(imsi_list=final_valid_imsi_list)
        )

        audit_details = []
        if final_valid_imsi_list:
            for sim_detail in final_valid_reponse:
                prior_profile = sim_profile_lookup.get(sim_detail.imsi, None)
                prior_msisdn = sim_imsi_lookup.get(sim_detail.imsi, None)
                # Adding audit details
                if prior_msisdn != sim_detail.msisdn:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_detail.imsi),
                            iccid=ICCID(sim_detail.iccid),
                            msisdn=MSISDN(sim_detail.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_msisdn,
                            new_value=MSISDN(sim_detail.msisdn),
                            field="MSISDN",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )
                if sim_detail.sim_profile and prior_profile != sim_detail.sim_profile:
                    audit_details.append(
                        model.SIMCardAudit(
                            uuid=uuid4(),
                            imsi=IMSI(sim_detail.imsi),
                            iccid=ICCID(sim_detail.iccid),
                            msisdn=MSISDN(sim_detail.msisdn),
                            request_type="Update MSISDN",
                            prior_value=prior_profile,
                            new_value=sim_detail.sim_profile,
                            field="SIM Profile",
                            action="Updated",
                            client_ip=client_ip,
                            created_by=uploaded_by,
                        )
                    )

            self.audit_service.add_upload_msisdn_audit_api(audit_details=audit_details)

        logger.info(f"Total Records: {total_records}")
        logger.info(f"Error Details: {error_response}")
        return model.BulkSimCardUpdateResult(
            total_details=total_records,
            error_details=len(error_response),
            error_results=error_response,
        )

    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        return self.sim_repository.unallocate_sim_cards(imsi_list=imsi_list)

    def imsis_to_delete(self, imsi_list: list[IMSI]) -> model.IMSIDeleteResponse:
        try:
            dict_reponse_count = self.sim_repository.imsis_to_delete(
                imsis_list=imsi_list
            )
            deleted = dict_reponse_count.get("deleted_imsis_count", 0)

            message = f"{deleted} out of {len(imsi_list)} IMSIs were deleted."

            return model.IMSIDeleteResponse(message=message)

        except Exception as e:
            logger.error(f"Error in imsis_to_delete: {str(e)}")
            raise

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        order_response = self.sim_repository.create_order(order=order)
        return order_response
