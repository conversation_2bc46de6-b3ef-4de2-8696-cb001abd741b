"""create tracking table

Revision ID: a7b8c9d0e1f2
Revises: f6a7b8c9d0e1
Create Date: 2025-05-21 17:51:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "a7b8c9d0e1f2"
down_revision = "f6a7b8c9d0e1"
branch_labels = None
depends_on = None


def upgrade():
    # tracking
    op.create_table(
        "tracking",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("reference_id", sa.String(length=100), unique=True),
        sa.Column("reference_url", sa.String(length=100), unique=True),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("tracking", schema="orders")
