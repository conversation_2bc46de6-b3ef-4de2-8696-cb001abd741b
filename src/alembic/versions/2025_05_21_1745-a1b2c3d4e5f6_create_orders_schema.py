"""create orders schema

Revision ID: a1b2c3d4e5f6
Revises: 0cf658516c83
Create Date: 2025-05-21 17:45:00.000000

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade():
    # Create orders schema
    op.execute("CREATE SCHEMA IF NOT EXISTS orders")


def downgrade():
    op.execute("DROP SCHEMA IF EXISTS orders")
