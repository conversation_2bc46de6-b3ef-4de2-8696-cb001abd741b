"""database tables for ordering process

Revision ID: 7b2811f9afc0
Revises: 0cf658516c83
Create Date: 2025-05-21 17:44:42.448796

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "7b2811f9afc0"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade():
    # Create orders schema
    op.execute("CREATE SCHEMA IF NOT EXISTS orders")

    # orders
    op.create_table(
        "orders",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
            nullable=False,
        ),
        sa.Column("notes", sa.Text),
        sa.Column("order_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )

    # customers
    op.create_table(
        "customers",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
            nullable=False,
        ),
        sa.Column("customer_name", sa.String(length=255)),
        sa.Column("customer_account_name", sa.String(length=255)),
        sa.Column("customer_account_id", sa.Integer),
        sa.Column("customer_account_logo_url", sa.String(length=255)),
        sa.Column("customer_email", sa.String(length=255)),
        sa.Column("customer_contact_no", sa.String(length=50)),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )

    # shipping_details
    op.create_table(
        "shipping_details",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("contact_name", sa.String(length=255)),
        sa.Column("address_line1", sa.String(length=255)),
        sa.Column("address_line2", sa.String(length=255)),
        sa.Column("city", sa.String(length=100)),
        sa.Column("state_or_region", sa.String(length=100)),
        sa.Column("postal_code", sa.String(length=20)),
        sa.Column("country", sa.String(length=100)),
        sa.Column("other_information", sa.Text),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )

    # items
    op.create_table(
        "items",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.Column("sim_type", sa.String(length=100)),
        sa.Column("quantity", sa.Integer),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )

    # status_history
    op.create_table(
        "status_history",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("status_name", sa.String(length=50)),
        sa.Column("status_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )

    # tracking
    op.create_table(
        "tracking",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("reference_id", sa.String(length=100), unique=True),
        sa.Column("reference_url", sa.String(length=100), unique=True),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("tracking", schema="orders")
    op.drop_table("status_history", schema="orders")
    op.drop_table("items", schema="orders")
    op.drop_table("shipping_details", schema="orders")
    op.drop_table("customers", schema="orders")
    op.drop_table("orders", schema="orders")
    op.execute("DROP SCHEMA IF EXISTS orders")
