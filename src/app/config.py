import inspect
import logging
import os
import secrets
import sys
from functools import wraps
from logging.config import dictConfig
from pathlib import Path
from typing import Any, Literal

import yaml
from pydantic import AnyHttpUrl, AnyUrl, BaseSettings, validator
from pydantic.env_settings import DotenvType
from pydantic.fields import <PERSON><PERSON><PERSON>

from app.constants import APP_DIR, MASK_LEN
from common.utils import get_trace_id


class DatabaseUri(AnyUrl):
    """Database URI type"""

    host_required = False


class Settings(BaseSettings):
    """Basic application settings"""

    class Config:
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"

    APPLICATION_ENV: Literal["prod", "local"] = "local"
    DEBUG: bool = True
    LOG_LEVEL: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "DEBUG"

    # application settings specific for dev environment
    APP_HOST: str = "localhost"
    APP_PORT: int = 8000

    DATABASE_URI: DatabaseUri | None = None
    MONGODB_DATABASE_URI: DatabaseUri | None = None

    # AWS config
    S3_REGION_NAME: str | None = None
    S3_ACCESS_KEY_ID: str | None = None
    S3_SECRET_ACCESS_KEY: str | None = None
    S3_ENDPOINT_URL: AnyUrl | None = None
    S3_CDR_BUCKET_NAME: str | None = None
    S3_CUSTOM_ALLOCATION_BUCKET_NAME: str | None = None
    S3_MEDIA_BUCKET_NAME: str | None = None
    USE_S3_STORAGE: bool = True
    S3_ERROR_CDR_FOLDER_NAME: str | None = None
    S3_DUPLICATE_CDR_FOLDER_NAME: str | None = None
    S3_MSISDN_UPLOAD_BUCKET_NAME: str | None = None
    S3_BULK_MSISDN_UPLOAD_BUCKET_NAME: str | None = None

    # PIP config
    PIP_URI: str | None = None
    PIP_USERNAME: str | None = None
    PIP_PASSWORD: str | None = None

    # PPL config
    PPL_URI: str | None = None
    PPL_CUSTOMER_STATUS: str | None = None
    PPL_CUSTOMER_STATUS_HLR: str | None = None
    PPL_SIM_ACTIVATE: str | None = None
    PPL_SIM_SUSPEND: str | None = None
    PPL_REGISTER: str | None = None
    PPL_REGISTER_UPDATE: str | None = None
    PPL_SIM_WORKSTATUS: str | None = None
    PPL_USERNAME: str | None = None
    PPL_PASSWORD: str | None = None

    # SPOG config
    SPOG_NOTIFICATION_URI: str | None = None
    LANDING_PAGE_URL: str | None = None
    WHITELISTED_IPS: str | None = None

    # OIDC config
    OIDC_CLIENT_ID: str | None = None
    OIDC_CLIENT_SECRET: str | None = None
    OIDC_AUTHORIZATION_URL: AnyHttpUrl | None = None
    OIDC_TOKEN_URL: AnyHttpUrl | None = None
    OIDC_TOKEN_INTROSPECTION_URL: AnyHttpUrl | None = None

    # Platform Core
    ORGANIZATIONS_URL: AnyHttpUrl | None = None

    # SAF / Digital Identity API(APIGEE)
    DIGITAL_IDENTITY_URL: AnyHttpUrl | None = None
    DIGITAL_IDENTITY_CLIENT_ID: str | None = None
    DIGITAL_IDENTITY_CLIENT_SECRET: str | None = None

    # Market Share API
    CDR_ANALYTICS_API: str | None = None
    MARKET_SHARE_SUMMARY: str | None = None
    IMSI_USAGE_ANALYTICS_API: str | None = None

    # Audit service API
    AUDIT_SERVICE_URL: str | None = None

    HEALTH_CHECK_SECRET: str = secrets.token_urlsafe(32)

    # Application Base URL
    APP_BASE_URL: AnyHttpUrl | None = None

    # Production Script Update File Path
    MSISDN_UPDATE_FILE: str | None = None
    IMSI_REALLOCATION_PATH: str | None = None

    # Allocation  file validation
    MAX_FILE_SIZE_MB: int | None = None

    # Production Script to add monthly data
    ADD_MONTHLY_DATA: str | None = None

    # 365 Days token data
    LONG_ACCESS_TOKEN: str | None = None

    # Read timeout
    TIMEOUT: int = 10

    def __init__(self, _env_file: DotenvType | None = None):
        super().__init__(_env_file)

    @validator(
        "OIDC_CLIENT_ID",
        "OIDC_CLIENT_SECRET",
        "OIDC_AUTHORIZATION_URL",
        "OIDC_TOKEN_URL",
        "OIDC_TOKEN_INTROSPECTION_URL",
        "LANDING_PAGE_URL",
    )
    def require_on_prod(cls, v: Any, values: dict[str, Any], field: ModelField):
        if "APPLICATION_ENV" not in values:
            raise AssertionError(
                (
                    "APPLICATION_ENV must be defined before "
                    "fields required in 'prod' environment."
                )
            )
        if values["APPLICATION_ENV"] == "prod" and v is None:
            raise ValueError(f"{field.name} is required for the 'prod' environment.")
        return v


def init_logging(settings: Settings, config_filename: str) -> None:
    """Initialize logging system based on the config"""
    otel_formatter = "default_with_otel"
    with open(config_filename, "r") as stream:
        logging_config = yaml.safe_load(stream)
        logging_config["loggers"]["root"]["level"] = settings.LOG_LEVEL
        if OTEL_ENABLED:
            logging_config["handlers"]["console"]["formatter"] = otel_formatter
        dictConfig(logging_config)

    logging.info(f"Logging system enabled, {OTEL_ENABLED=}.")


def _dump_dotenv(env_file: DotenvType) -> None:
    """Log dotenv files used for environment"""
    if env_file is None:
        logging.debug("no dotenv files passed")
        return

    dotenvs = [env_file] if isinstance(env_file, (str, os.PathLike)) else env_file

    for dotenv in dotenvs:
        path = Path(dotenv).expanduser().absolute()
        if not path.exists():
            logging.debug("configuration file %s does not exist", path)
        else:
            logging.debug("using configuration file %s", path)


TEST_WITH_PYTEST = "pytest" in sys.modules
OTEL_ENABLED = "opentelemetry" in sys.modules

# .env file is used as a default configuration,
# it can be overriden with test.env when running tests
ENV_FILE: DotenvType = (".env", "test.env") if TEST_WITH_PYTEST else ".env"

settings = Settings(_env_file=ENV_FILE)

init_logging(settings, str(APP_DIR.parent / "logging.yaml"))

_dump_dotenv(ENV_FILE)


class TraceIDAdapter(logging.LoggerAdapter):
    def process(self, msg, kwargs):
        trace_id = get_trace_id()
        frame = inspect.currentframe()
        calling_frame = frame.f_back.f_back.f_back
        function_name = calling_frame.f_code.co_name
        return f"[trace_id: {trace_id}] [function: {function_name}] {msg}", kwargs


logger = TraceIDAdapter(logging.getLogger(__name__))


class SensitiveDataFilter(logging.Filter):
    def __init__(self, sensitive_value, mask_length):
        super().__init__()
        self.sensitive_value = sensitive_value
        self.mask_length = mask_length

    def filter(self, record):
        if self.sensitive_value:
            mask_len = len(self.sensitive_value) - self.mask_length
            mask = "*" * mask_len
            record.msg = record.msg.replace(
                self.sensitive_value,
                mask.join(
                    [
                        self.sensitive_value[: self.mask_length],
                        self.sensitive_value[-self.mask_length :],
                    ]
                ),
            )
        return True


def log_activity(logging, ignored_params=(), sensitive_values=()):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            filtered_kwargs = {
                key: value for key, value in kwargs.items() if key not in ignored_params
            }
            logging.info(f"Request parameter: {filtered_kwargs}")
            try:
                result = func(*args, **kwargs)
                for attr_name in sensitive_values:
                    attr_value = getattr(result, attr_name)
                    logging.addFilter(SensitiveDataFilter(attr_value, MASK_LEN))
                logging.info(f"Response of {func.__name__}: {result}")
                return result
            except Exception as e:
                logging.error(f"Error in function {func.__name__}: {str(e)}")
                raise

        return wrapper

    return decorator


def log_cdr_push(logging, ignored_params=(), sensitive_values=(), sensitive_tags=()):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            filtered_kwargs = {
                key: value for key, value in kwargs.items() if key not in ignored_params
            }
            if "xml_string" in filtered_kwargs:
                xml_string = filtered_kwargs["xml_string"]
                for tag in sensitive_tags:
                    start_tag = f"<{tag}>"
                    end_tag = f"</{tag}>"
                    start_index = xml_string.find(start_tag)
                    end_index = xml_string.find(end_tag)
                    if start_index != -1 and end_index != -1:
                        masked_value = xml_string[
                            start_index + len(start_tag) : end_index
                        ]
                        masked_value = (
                            masked_value[:MASK_LEN]
                            + "*" * (len(masked_value) - MASK_LEN)
                            + masked_value[-MASK_LEN:]
                        )
                        xml_string = (
                            xml_string[: start_index + len(start_tag)]
                            + masked_value
                            + xml_string[end_index:]
                        )
                filtered_kwargs["xml_string"] = xml_string
            logging.info(f"Request parameter: {filtered_kwargs}")
            try:
                result = func(*args, **kwargs)
                for attr_name in sensitive_values:
                    attr_value = getattr(result, attr_name)
                    logging.addFilter(SensitiveDataFilter(attr_value, MASK_LEN))
                logging.info(f"Response of {func.__name__}: {result}")
                return result
            except Exception as e:
                logging.error(f"Error in function {func.__name__}: {str(e)}")
                raise

        return wrapper

    return decorator
