import logging
from collections.abc import Generator
from functools import cache

from fastapi import Depends
from sqlalchemy.orm import Session

from api import deps
from api.deps import get_auth_service, get_authenticated_user
from api.deps import get_db_session as _get_db_session
from api.deps import get_redis_client
from app.config import settings
from app.platform import get_auditlog_api_client, get_market_share_api_client
from auth.dto import AuthenticatedUser
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from cdrdata.adapters.repository import AbstractCdrRepository, DatabaseCdrRepository
from common.file_storage import AbstractFileStorage
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
)
from redis.adapters.externalapi import HTTPRedisAPI
from sim.adapters.externalapi import (
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
    AuditServiceAPI,
    FakeAuditServiceAPI,
    FakeMarketShareAPI,
    HTTPMarketShareAPI,
    SimProvisioning,
)
from sim.adapters.repository import AbstractSimRepository, DatabaseSimRepository
from sim.monitoring_service import AbstractSimMonitoringService, SimMonitoringService
from sim.notification_service import AbstractNotificationService, SimNotification
from sim.proxies import SimServiceAuthProxy
from sim.services import AbstractSimService, FakeMediaService, MediaService, SimService


def get_sim_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractSimRepository:
    return DatabaseSimRepository(session)


def _rate_plan_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractRatePlanRepository:
    return DatabaseRatePlanRepository(session)


def _provisioning() -> AbstractSIMProvisioningAPI:
    return SimProvisioning()


def _cdr_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractCdrRepository:
    return DatabaseCdrRepository(session)


def get_media_service(
    storage: AbstractFileStorage = Depends(
        deps.get_file_storage_object(settings.S3_MEDIA_BUCKET_NAME)
    ),
) -> MediaService:
    return MediaService(storage)


@cache
def fake_market_share_api() -> AbstractMarketShareAPI:
    return FakeMarketShareAPI()


@cache
def fake_audit_service_api():
    return FakeAuditServiceAPI()


def get_market_share_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AbstractMarketShareAPI, None, None]:
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )

    if auth_token:
        with get_market_share_api_client(auth_token) as client:
            yield HTTPMarketShareAPI(client)
    else:
        yield fake_market_share_api()


def get_audit_service_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AuditServiceAPI, None, None]:
    logging.error("Audit auth service")
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )
    logging.error(f"Audit_auth token: {auth_token}")
    if auth_token:
        with get_auditlog_api_client(auth_token) as client:
            yield AuditServiceAPI(client)
    else:
        yield fake_audit_service_api()


def _get_audit_service_client() -> Generator[AuditServiceAPI, None, None]:
    logging.error("Audit_auth service")
    auth_token: str | None
    auth_token = settings.LONG_ACCESS_TOKEN
    logging.error(f"Audit_auth token: {auth_token}")
    if auth_token:
        with get_auditlog_api_client(auth_token) as client:
            yield AuditServiceAPI(client)
    else:
        yield fake_audit_service_api()


def sim_service(
    sim_repository: AbstractSimRepository = Depends(get_sim_repository),
    rate_plan_repository: AbstractRatePlanRepository = Depends(_rate_plan_repository),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    provisioning: AbstractSIMProvisioningAPI = Depends(_provisioning),
    cdr_repository: AbstractCdrRepository = Depends(_cdr_repository),
    market_share_api: AbstractMarketShareAPI = Depends(get_market_share_client),
    audit_service: AuditServiceAPI = Depends(get_audit_service_client),
    media_service: MediaService = Depends(get_media_service),
    redis_api: HTTPRedisAPI = Depends(get_redis_client),
) -> AbstractSimService:
    logging.error("Calling deps service")
    return SimServiceAuthProxy(
        sim_service=SimService(
            sim_repository,
            rate_plan_repository,
            provisioning,
            cdr_repository,
            market_share_api,
            audit_service,
            media_service,
            redis_api,
        ),
        user=authenticated_user,
    )


def push_notification_service(
    sim_repository: AbstractSimRepository = Depends(get_sim_repository),
    audit_service: AuditServiceAPI = Depends(_get_audit_service_client),
) -> AbstractNotificationService:
    return SimNotification(
        sim_repository,
        audit_service,
    )


def fake_media_service() -> FakeMediaService:
    return FakeMediaService()


def sim_monitoring_service(
    sim_repository: AbstractSimRepository = Depends(get_sim_repository),
    audit_service: AuditServiceAPI = Depends(get_audit_service_client),
) -> AbstractSimMonitoringService:
    return SimMonitoringService(
        sim_repository,
        audit_service,
    )
