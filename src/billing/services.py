from abc import ABC, abstractmethod
from datetime import datetime
from decimal import Decimal
from functools import reduce
from operator import attrgetter
from typing import Iterable, Iterator

from sqlalchemy.orm import Session

from accounts.domain.model import AccountStatus
from app.config import logger
from billing.adapters.repository import (
    AbstractAccountRepository,
    AbstractBillingCycleRepository,
)
from billing.domain import model
from billing.exceptions import (
    BillingCycleDoesNotExist,
    InvoiceAlreadyPublished,
    InvoiceDoesNotExist,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import IMSI, Month, Service, ServiceUsage, SimStatus
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from rate_plans.domain.model import RatePlans
from rating.adapters.usage_repository import AbstractUsageRepository
from rating.calculation import RatingCalculator
from sim.adapters.repository import AbstractSimRepository


class AbstractSimUsageService(ABC):
    @abstractmethod
    def copy_usage_for_billing(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[IMSI] = None
    ) -> None:
        ...

    @abstractmethod
    def remove_billing_usage(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[model.IMSI]
    ) -> None:
        ...


class AbstractBillingService(ABC):
    @abstractmethod
    def generate_invoices(
        self, month: Month, account_ids: list[int] | None = None
    ) -> None:
        ...

    @abstractmethod
    def get_invoices(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        ...

    @abstractmethod
    def get_invoices_new(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        ...

    @abstractmethod
    def get_invoice(self, invoice_id: int) -> model.Invoice:
        ...

    @abstractmethod
    def remove_invoices(self, month: Month) -> None:
        ...

    @abstractmethod
    def unpublish_invoice(self, invoice_id: int) -> None:
        ...

    @abstractmethod
    def publish_invoice(self, invoice_id: int) -> model.Invoice:
        ...

    @abstractmethod
    def add_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> model.Adjustment:
        ...

    @abstractmethod
    def remove_invoice_adjustment(self, invoice_id: int, adjustment_id: int) -> None:
        ...

    @abstractmethod
    def update_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> None:
        ...

    @abstractmethod
    def get_sim_usage(
        self,
        invoice: model.Invoice,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.SubscriptionSIM], int]:
        ...

    @abstractmethod
    def get_sim_usage_export(
        self,
        invoice: model.Invoice,
    ) -> list[dict]:
        ...

    @abstractmethod
    def get_sim_total_usage(
        self, invoice: model.Invoice, searching: Searching | None = None
    ) -> tuple[Iterable[ServiceUsage], Decimal]:
        ...

    @abstractmethod
    def invoice_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        ...

    @abstractmethod
    def get_monthly_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        ...


class BillingService(AbstractBillingService):
    def __init__(
        self,
        account_repository: AbstractAccountRepository,
        billing_cycle_repository: AbstractBillingCycleRepository,
        rate_plan_repository: AbstractRatePlanRepository,
        sim_repository: AbstractSimRepository,
        sim_usage_service: AbstractSimUsageService,
    ):
        self.accounts = account_repository
        self.billing_cycles = billing_cycle_repository
        self.rate_plans = rate_plan_repository
        self.sims = sim_repository
        self.sim_usage = sim_usage_service

    def generate_invoices(
        self, month: Month, account_ids: list[int] | None = None
    ) -> None:
        bc = self.billing_cycles.get(month)
        if bc is None:
            bc = model.BillingCycle(month)
            self.billing_cycles.add(bc)

        accounts = self.accounts.query(account_ids)
        for account in accounts:
            if account.status is AccountStatus.CLOSED:
                continue

            invoice = bc.get_invoice(account.id)
            if invoice is None:
                invoice = bc.make_invoice(account.id, account.payment_terms)
            elif invoice.rating_state == model.InvoiceRatingState.IN_PROGRESS:
                continue

            self.billing_cycles.add(bc)

            try:
                invoice.reset(account.payment_terms)
            except InvoiceAlreadyPublished:
                continue

            try:
                self.generate_invoice(bc, invoice, account.sim_charge)
            except Exception as exc:
                invoice.rating_state = model.InvoiceRatingState.FAILED
                logger.error(f"Invoice generation failed:{account.id=}, {exc=}")
            else:
                invoice.rating_state = model.InvoiceRatingState.GENERATED

            self.billing_cycles.add(bc)

        self.billing_cycles.add(bc)

    def generate_invoice(
        self, bc: model.BillingCycle, invoice: model.Invoice, sim_charge: Decimal
    ) -> None:

        account_sims = self.sims.get_sim_cards(invoice.account_id)
        account_imsis = [s.imsi for s in account_sims]
        self.sim_usage.remove_billing_usage(bc, account_imsis)
        self.billing_cycles.reset_invoice_allocation_details(invoice.id)

        rate_plan_map = {}
        for rate_plan in self.rate_plans.query([invoice.account_id]):
            rate_plan_map[rate_plan.id] = rate_plan
            sims_total = self.sims.get_sim_count(rate_plan_ids=[rate_plan.id])
            invoice.add_subscription(
                rate_plan.id,
                rate_plan.name,
                access_fee=rate_plan.access_fee,
                sims_total=sims_total,
                sim_charge=sim_charge,
            )

        active_sims = self.sims.cards_active_statistic(
            account_id=invoice.account_id, month=bc.month
        )
        for sim in active_sims:
            if sim.rate_plan_id is None:
                raise AssertionError(
                    f"Active SIM card must be assigned to the rate plan({sim.id=})."
                )
            # New billing logic
            self.billing_cycles.invoice_allocation_details(
                invoice.id, [sim.imsi], bc.month
            )
            if sim.sim_status != SimStatus.READY_FOR_ACTIVATION:
                invoice.add_sim(
                    rate_plan_id=sim.rate_plan_id,
                    sim_id=sim.id,
                    imsi=sim.imsi,
                    iccid=sim.iccid,
                    msisdn=sim.msisdn,
                    first_time_activated=sim.is_first_activation,
                    sim_status=sim.sim_status,
                )
            # New billing logic

        self.billing_cycles.reset_invoice_account_charge(invoice.id)
        self.billing_cycles.invoice_account_charge(
            invoice.account_id, invoice.id, bc.month
        )

        self.billing_cycles.reset_invoice_overage_charge(invoice.id)
        self.billing_cycles.invoice_overage_charge(
            invoice.account_id, invoice.id, bc.month
        )

        for subscription in invoice.subscriptions:
            imsi_list = [s.imsi for s in subscription.sims]
            self.sim_usage.copy_usage_for_billing(bc, imsi_list)
            rate_plan = rate_plan_map[subscription.rate_plan_id]

            charged_usage = self._charge_usage(bc, rate_plan, imsi_list)
            self.billing_cycles.update_sim_usage(bc, charged_usage)

    def _charge_usage(
        self, bc: model.BillingCycle, rate_plan: RatePlans, imsi_list: list[IMSI]
    ) -> Iterator[model.SimUsage]:
        for rate_group in rate_plan._rate_groups:
            usage = self.billing_cycles.query_sim_usage(
                bc, imsi_list, set(rate_group.services)
            )
            calculator = RatingCalculator.from_rate_group(rate_group)
            for record in usage:
                record.charge = calculator.apply_rates(
                    record.volume, rate_group.rate_model_id, record.service.value
                )
                yield record

    def get_invoices(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        bc = None
        if month is not None:
            bc = self.billing_cycles.get(month)
            if not bc:
                raise BillingCycleDoesNotExist()

        invoices = self.billing_cycles.get_invoices(bc, account_ids)
        for invoice in invoices:
            invoice.usages = self._usages(invoice)
            yield invoice

    def get_invoices_new(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        bc = None
        if month is not None:
            bc = self.billing_cycles.get(month)
            if not bc:
                raise BillingCycleDoesNotExist()

        invoices = self.billing_cycles.get_invoices_new(
            bc, account_ids=account_ids, month=month
        )
        return invoices

    def get_invoice(self, invoice_id: int) -> model.Invoice:
        invoice = self._get_unfilled_invoice(invoice_id)
        invoice.usages = self._usages(invoice)
        total_account_charge = self._total_account_charge(invoice)
        invoice._total_account_charge = total_account_charge.account_charge
        return invoice

    def _get_unfilled_invoice(self, invoice_id: int) -> model.Invoice:
        """Return invoice without calculated data."""
        invoice = self.billing_cycles.get_invoice_by_id(invoice_id)
        if invoice is None:
            raise InvoiceDoesNotExist(invoice_id)
        bc = invoice.billing_cycle
        if not bc:
            raise AssertionError("Billing cycle for the invoice must exists")
        bc.month = Month.from_date(bc.month)
        return invoice

    def _usages(self, invoice: model.Invoice) -> list[ServiceUsage]:
        active_sims = self.billing_cycles.get_subscription_sims(invoice.id)
        active_imsis = [s.imsi for s in active_sims]
        service_usages = []
        for usage in self.billing_cycles.get_service_usages(
            invoice.billing_cycle, active_imsis, invoice.id
        ):
            service_usages.append(usage)

        return list(sorted(service_usages, key=attrgetter("service")))

    def _total_account_charge(
        self, invoice: model.Invoice
    ) -> model.InvoiceFixedChargeDetails:
        total_account_charge = (
            self.billing_cycles.get_total_account_charge_by_invoice_id(invoice.id)
        )
        return total_account_charge

    def remove_invoices(self, month: Month) -> None:
        bc = self.billing_cycles.get(month)
        if not bc:
            raise BillingCycleDoesNotExist()

    def unpublish_invoice(self, invoice_id: int) -> None:
        invoice = self._get_unfilled_invoice(invoice_id)
        invoice.unpublish()
        self.billing_cycles.update_invoice(invoice)

    def publish_invoice(self, invoice_id: int) -> model.Invoice:
        invoice = self.get_invoice(invoice_id)
        invoice.publish()
        self.billing_cycles.update_invoice(invoice)
        return invoice

    def add_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> model.Adjustment:
        invoice = self._get_unfilled_invoice(invoice_id)
        adjustment = invoice.add_adjustment(adjustment)
        self.billing_cycles.update_invoice(invoice)
        return adjustment

    def remove_invoice_adjustment(self, invoice_id: int, adjustment_id: int) -> None:
        invoice = self._get_unfilled_invoice(invoice_id)
        adjustment = invoice.get_adjustment(adjustment_id)
        invoice.remove_adjustment(adjustment)
        return self.billing_cycles.update_invoice(invoice)

    def update_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> None:
        if adjustment.id is None:
            raise AssertionError("Adjustment ID must not be None")
        invoice = self._get_unfilled_invoice(invoice_id)
        old = invoice.get_adjustment(adjustment.id)
        invoice.remove_adjustment(old)
        invoice.add_adjustment(adjustment)
        return self.billing_cycles.update_invoice(invoice)

    def get_sim_usage(
        self,
        invoice: model.Invoice,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.SubscriptionSIM], int]:
        invoice = self._get_unfilled_invoice(invoice.id)
        sim_cards = self.billing_cycles.get_subscription_sims(
            invoice.id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        total_count = self.billing_cycles.get_subscription_sims_count(
            invoice.id, searching=searching
        )
        return sim_cards, total_count

    def get_sim_usage_export(
        self,
        invoice: model.Invoice,
    ) -> list[dict]:
        invoice = self._get_unfilled_invoice(invoice.id)
        sim_cards = self.billing_cycles.get_subscription_sims_export(
            invoice.id,
        )
        return sim_cards

    def get_sim_total_usage(
        self, invoice: model.Invoice, searching: Searching | None = None
    ) -> tuple[Iterable[ServiceUsage], Decimal]:
        sim_cards = self.billing_cycles.get_subscription_sims(
            invoice.id,
            searching=searching,
        )
        subscriptions_charge = Decimal(0)
        imsi_list = []

        for sim_card in sim_cards:
            subscriptions_charge += sim_card.subscription.charge
            imsi_list.append(sim_card.imsi)

        service_usage = self.billing_cycles.get_service_usages(
            invoice.billing_cycle, imsi_list=imsi_list, invoice_id=invoice.id
        )
        return service_usage, subscriptions_charge

    def _update_details(
        self,
        details: model.ReconciliationDetails | None,
        sim_data: model.ReconciliationAgg,
    ) -> model.ReconciliationDetails:
        if not details:
            details = model.ReconciliationDetails(
                simImsi=sim_data.simImsi, cdrImsi=sim_data.cdrImsi
            )

        sim_usage = sim_data.simUsage
        cdr_usage = sim_data.cdrUsage
        sim_cdr_variance = sim_data.variance

        service = sim_data.service
        if service == "DATA":
            details.dataDetails = model.ReconciliationDifference(
                simUsage=sim_usage, cdrUsage=cdr_usage, variance=sim_cdr_variance
            )
        elif service == "VOICE":
            details.voiceDetails = model.ReconciliationDifference(
                simUsage=sim_usage, cdrUsage=cdr_usage, variance=sim_cdr_variance
            )
        elif service == "SMS":
            details.smsDetails = model.ReconciliationDifference(
                simUsage=sim_usage, cdrUsage=cdr_usage, variance=sim_cdr_variance
            )

        return details

    def _from_to_date(self, billing_cycle: model.BillingCycle):
        billing_cycle_to_year = billing_cycle.month.year
        billing_cycle_to_month = list(
            map(
                lambda month: month + 1 if month != 12 and month < 12 else 1,
                [billing_cycle.month.month],
            )
        )[0]

        billing_cycle_to_year = list(
            map(
                lambda year: year + 1 if billing_cycle.month.month == 12 else year,
                [billing_cycle_to_year],
            )
        )[0]

        from_date = datetime.date(
            datetime(
                billing_cycle.month.year,
                billing_cycle.month.month,
                billing_cycle.month.day,
            )
        )
        to_date = datetime.date(
            datetime(
                billing_cycle_to_year, billing_cycle_to_month, billing_cycle.month.day
            )
        )

        return from_date, to_date

    def invoice_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        bc = None
        if month is not None:
            bc = self.billing_cycles.get(month)
            if not bc:
                raise BillingCycleDoesNotExist()

        from_date, to_date = self._from_to_date(billing_cycle=bc)

        sim_usage_result = self.billing_cycles.get_invoice_reconciliation(
            from_date=from_date, to_date=to_date, billing_cycle=bc
        )

        usage_data: list[model.ReconciliationDetails] = reduce(
            lambda acc, sim_data: list(
                filter(
                    lambda x: (
                        (
                            x.simImsi != sim_data.simImsi
                            if sim_data.simImsi is not None
                            else (
                                x.cdrImsi != sim_data.cdrImsi
                                if sim_data.cdrImsi is not None
                                else False
                            )
                        )
                    ),
                    acc,
                )
            )
            + [
                self._update_details(
                    next(
                        filter(
                            lambda x: (  # type: ignore
                                x.simImsi == sim_data.simImsi  # type: ignore
                                if sim_data.simImsi is not None
                                else (
                                    x.cdrImsi == sim_data.cdrImsi  # type: ignore
                                    if sim_data.cdrImsi is not None
                                    else False
                                )
                            ),
                            acc,
                        ),
                        None,
                    ),
                    sim_data,
                )
            ],
            sim_usage_result,
            [],
        )

        if usage_data == []:
            usage_data_message = "Success: all the data matched."
            return usage_data_message
        return usage_data

    def get_monthly_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        bc = None
        if month is not None:
            bc = self.billing_cycles.get(month)
            if not bc:
                raise BillingCycleDoesNotExist()

        from_date, to_date = self._from_to_date(billing_cycle=bc)

        sim_usage_result = self.billing_cycles.get_monthly_reconciliation(
            from_date=from_date, to_date=to_date
        )

        usage_data: list[model.ReconciliationDetails] = reduce(
            lambda acc, sim_data: list(
                filter(
                    lambda x: (
                        (
                            x.simImsi != sim_data.simImsi
                            if sim_data.simImsi is not None
                            else (
                                x.cdrImsi != sim_data.cdrImsi
                                if sim_data.cdrImsi is not None
                                else False
                            )
                        )
                    ),
                    acc,
                )
            )
            + [
                self._update_details(
                    next(
                        filter(
                            lambda x: (  # type: ignore
                                x.simImsi == sim_data.simImsi  # type: ignore
                                if sim_data.simImsi is not None
                                else (
                                    x.cdrImsi == sim_data.cdrImsi  # type: ignore
                                    if sim_data.cdrImsi is not None
                                    else False
                                )
                            ),
                            acc,
                        ),
                        None,
                    ),
                    sim_data,
                )
            ],
            sim_usage_result,
            [],
        )

        if usage_data == []:
            usage_data_message = "Success: all the data matched."
            return usage_data_message
        return usage_data


class SimUsageService(AbstractSimUsageService):
    def __init__(
        self,
        usage_repository: AbstractUsageRepository,
        billing_cycle_repository: AbstractBillingCycleRepository,
    ):
        self.usage = usage_repository
        self.billing_cycles = billing_cycle_repository

    def copy_usage_for_billing(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[IMSI] = None
    ) -> None:
        usage_records = self.usage.aggregate_month(
            month=billing_cycle.month,
            imsi_list=list(imsis) if imsis is not None else None,
        )
        billing_cycle.insert_sim_usage(
            (
                model.SimUsage(
                    imsi=r["imsi"],
                    service=r["service"],
                    volume=r["volume"] or 0,
                )
                for r in usage_records
            )
        )
        self.billing_cycles.add(billing_cycle)

    def remove_billing_usage(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[model.IMSI]
    ) -> None:
        billing_cycle.remove_sim_usage_by_imsis(imsis)
        self.billing_cycles.add(billing_cycle)


class SqlSimUsageService(AbstractSimUsageService):
    def __init__(self, session: Session):
        self.session = session

    def copy_usage_for_billing(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[IMSI] = None
    ) -> None:
        from sqlalchemy import func, insert, literal, select

        from billing.adapters import orm
        from rating.adapters.orm import monthly_usage_records as source

        billing_cycle_id = self.session.execute(
            select(orm.billing_cycle.c.id).where(
                orm.billing_cycle.c.month == billing_cycle.month
            )
        ).scalar_one_or_none()
        if not billing_cycle_id:
            raise AssertionError("Billing cycle must be created")

        where_clause = source.c.month == billing_cycle.month
        if imsis is not None:
            where_clause &= source.c.imsi.in_(list(imsis))

        select_from = (
            select(
                literal(billing_cycle_id),
                source.c.imsi,
                source.c.service,
                func.coalesce(func.sum(source.c.volume), 0),
            )
            .where(where_clause)
            .group_by(
                source.c.imsi,
                source.c.service,
            )
        )
        ins = insert(orm.sim_usage).from_select(
            [
                orm.sim_usage.c.billing_cycle_id,
                orm.sim_usage.c.imsi,
                orm.sim_usage.c.service,
                orm.sim_usage.c.volume,
            ],
            select_from,
        )
        self.session.execute(ins)
        self.session.commit()

    def remove_billing_usage(
        self, billing_cycle: model.BillingCycle, imsis: Iterable[model.IMSI]
    ) -> None:
        from sqlalchemy import delete, select

        from billing.adapters import orm

        billing_cycle_id = self.session.execute(
            select(orm.billing_cycle.c.id).where(
                orm.billing_cycle.c.month == billing_cycle.month
            )
        ).scalar_one_or_none()
        if not billing_cycle_id:
            raise AssertionError("Billing cycle must be created")

        delete_sim_usage = delete(orm.sim_usage).where(
            orm.sim_usage.c.billing_cycle_id == billing_cycle_id,
            orm.sim_usage.c.imsi.in_(list(imsis)),
        )
        self.session.execute(delete_sim_usage)
        self.session.commit()


class FakeBillingService:
    def __init__(self):
        self.invoices: list[model.Invoice] = []

    def get_invoice(
        self, invoice_id: int, brief_only: bool = False
    ) -> model.Invoice | None:
        for invoice in self.invoices:
            if invoice.id == invoice_id:
                return invoice
        else:
            return None

    def get_invoices(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        for invoice in self.invoices:
            filtered_by_billing_cycle = (
                month is not None and invoice.billing_cycle.month != month
            )
            filtered_by_account_id = (
                account_ids is not None and invoice.account_id not in account_ids
            )
            if filtered_by_billing_cycle or filtered_by_account_id:
                continue
            yield invoice

    def add_invoices(self, invoices: list[model.Invoice]):
        self.invoices.extend(invoices)

    def get_sim_usage(
        self,
        invoice: model.Invoice,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.SubscriptionSIM], int]:
        sims: dict[IMSI, model.SubscriptionSIM] = {}
        for sim_usage in invoice.billing_cycle.sim_usage:
            sim = sims.setdefault(
                sim_usage.imsi,
                model.SubscriptionSIM(
                    sim_id=1,
                    iccid=12345324342345234223,  # type: ignore
                    msisdn=************,  # type: ignore
                    imsi=sim_usage.imsi,
                    first_time_activated=False,
                ),
            )
            sim.usage.append(sim_usage)
            sim.subscription = invoice.subscriptions[0]
        return list(sims.values()), len(sims)

    def get_sim_total_usage(
        self, invoice: model.Invoice, searching: Searching | None = None
    ) -> tuple[Iterable[ServiceUsage], Decimal]:
        return (
            [
                model.ServiceUsage(
                    service=s,
                    volume=1,
                    bulk_overage_charge=Decimal(0),
                    sub_charge=Decimal(0),
                    total_overage_charge=Decimal(0),
                    charge=Decimal(1),
                )
                for s in Service
            ],
            Decimal(0),
        )
