import math
from copy import deepcopy
from datetime import datetime
from io import BytesIO
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch
from uuid import uuid4

from fastapi import BackgroundTasks, HTTPException, UploadFile, status

from api.rate_plans.deps import (
    account_resolver,
    fake_account_resolver,
    fake_media_service,
    get_media_service,
    get_rate_plan_repository,
)
from api.schema_types import PaginatedResponse
from api.sim.deps import _cdr_repository, _rate_plan_repository
from api.sim.deps import fake_media_service as fake_media_service_sim_service
from api.sim.deps import get_media_service as get_media_service_sim_service
from api.sim.deps import get_sim_repository
from api.sim.endpoints import (
    connection_history_export,
    create_order,
    delete_imsis,
    get_available_msisdn_count,
    get_msisdn_export,
    get_msisdn_factor,
    get_msisdn_pool_details,
    re_allocation_process,
    sms_connection_history_export,
    unallocate_imsis,
    update_selected_sim_details,
    update_sim_card_details,
    upload_msisdn,
    validate_rate_plan_change,
    voice_connection_history_export,
)
from api.sim.schemas import (
    BulkUpdateRequest,
    CreateOrderRequest,
    CreateOrderResponse,
    IMSIDeleteResponse,
    MSISDNMapping,
    MsisdnPoolExport,
    ReAllocation,
    SimCDRHistory,
    SimSMSCDRHistory,
    SimVoiceCDRHistory,
    UnallocateSimCardDetails,
)
from authorization.services import AbstractAuthorizationAPI
from cdrdata.adapters.repository import AbstractCdrRepository, InMemoryCdrRepository
from common.file_storage import AbstractFileStorage
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain.model import RatePlan
from redis.adapters.externalapi import FakeRedisAPI, HTTPRedisAPI
from sim import exceptions
from sim.adapters.externalapi import (
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
    FakeAuditServiceAPI,
    FakeMarketShareAPI,
    FakeSIMProvisioningAPI,
)
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository
from sim.domain import model
from sim.domain.ports import AbstractAuditService
from sim.exceptions import (
    IMSIDoesNotExit,
    IMSINotAllocated,
    NoConnectionHistory,
    NotFound,
    UnallocationException,
)
from sim.services import AbstractSimService, MediaService, SimService

import pytest  # isort: skip


@pytest.fixture
def sim_repository() -> AbstractSimRepository:
    return InMemorySimRepository()


@pytest.fixture
def rate_plan_repository() -> AbstractRatePlanRepository:
    return InMemoryRatePlanRepository([])


@pytest.fixture
def provisioning() -> AbstractSIMProvisioningAPI:
    return FakeSIMProvisioningAPI()


@pytest.fixture
def cdr_repository() -> AbstractCdrRepository:
    return InMemoryCdrRepository()


@pytest.fixture
def market_share_api() -> AbstractMarketShareAPI:
    return FakeMarketShareAPI()


@pytest.fixture
def audit_service_api():
    return FakeAuditServiceAPI()


@pytest.fixture
def file_storage():
    mock_file_storage = Mock(spec=AbstractFileStorage)
    return mock_file_storage


@pytest.fixture
def media_service_details(file_storage):
    return MediaService(file_storage)


@pytest.fixture
def mock_media_service():
    media_service_mock = MagicMock(spec=MediaService)
    media_service_mock.get_file_url.return_value = "http://localhost:8000//home/<USER>/tmp"
    return media_service_mock


@pytest.fixture
def redis_service_api():
    return FakeRedisAPI()


@pytest.fixture
def service(
    sim_repository,
    rate_plan_repository,
    provisioning,
    cdr_repository,
    market_share_api,
    audit_service_api,
    mock_media_service,
    redis_service_api,
) -> SimService:
    return SimService(
        sim_repository,
        rate_plan_repository,
        provisioning,
        cdr_repository,
        market_share_api,
        audit_service_api,
        mock_media_service,
        redis_service_api,
    )


@pytest.fixture(autouse=True)
def override_dependencies(
    client,
    sim_repository,
    rate_plan_repository,
    cdr_repository,
):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    overrides[get_sim_repository] = lambda: sim_repository
    overrides[_cdr_repository] = lambda: cdr_repository
    overrides[_rate_plan_repository] = lambda: rate_plan_repository
    overrides[get_rate_plan_repository] = lambda: rate_plan_repository
    overrides[account_resolver] = fake_account_resolver
    overrides[get_media_service] = fake_media_service
    overrides[get_media_service_sim_service] = fake_media_service_sim_service
    yield
    client.app.dependency_overrides = backup_deps


@pytest.fixture
def create_sim_cards():
    def factory(length, imsi_first="***************"):
        sim_cards = []
        for i in range(length):
            sim_cards.append(
                model.SIMCard(
                    iccid=f"{75647583748396758496 + i}",
                    imsi=f"{int(imsi_first) + i}",
                    msisdn=f"{************ + i}",
                )
            )
        return sim_cards

    return factory


@pytest.fixture
def create_range(service: SimService):
    def factory(
        sim_cards: list[model.SIMCard],
        msisdn_pool: list[model.MsisdnPool] | None = None,
        **kwargs,
    ):
        first_sim_card_imsi = sim_cards[0].imsi if sim_cards else None
        last_sim_card_imsi = sim_cards[-1].imsi if sim_cards else None

        _kwargs = {
            "title": "Reference",
            "form_factor": "MICRO",
            "id": 1,
            "created_at": datetime.now(),
            "created_by": "John Billing",
            "quantity": len(sim_cards),
            "imsi_first": first_sim_card_imsi,
            "imsi_last": last_sim_card_imsi,
            "remaining": len(sim_cards),
            **kwargs,
        }
        range_ = model.Range(**_kwargs)  # type: ignore
        if sim_cards is not None:
            range_.sim_cards = sim_cards
        service.sim_repository.add_range(range_, msisdn_pool=msisdn_pool)
        return range_

    return factory


@pytest.fixture
def create_rate_plan(rate_plan_repository):  # temp name
    def factory(**kwargs):
        _kwargs = {
            "account_id": 1,
            "name": "test",
            "access_fee": 0.2,
            "sim_limit": 5,
            "is_default": False,
            **kwargs,
        }
        rate_plan = RatePlan(**_kwargs)
        return rate_plan_repository.add(rate_plan)

    return factory


@pytest.fixture
def create_allocation(service: SimService):
    def factory(**kwargs):
        _kwargs = {
            "title": "Reference",
            "account_id": 1,
            "range_id": 1,
            "quantity": 2,
            "created_at": datetime.now(),
            "rate_plan_id": 1,
            **kwargs,
        }
        allocation = model.Allocation(**_kwargs)
        return service.add_allocation(allocation)

    return factory


@pytest.fixture
def create_allocation_data():
    def factory(**kwargs):
        return {
            "title": "Reference",
            "accountId": 1,
            "rangeId": 1,
            "ratePlanId": 1,
            "quantity": 2,
            **kwargs,
        }

    return factory


@pytest.fixture
def mock_dependencies():
    return {
        "request_mock": MagicMock(),
        "authorization_mock": MagicMock(),
        "sim_service_mock": MagicMock(),
    }


class TestCreateAllocation:
    def test_happy_path_is_ok(
        self,
        client,
        create_sim_cards,
        create_rate_plan,
        create_range,
        create_allocation_data,
    ):
        rate_plan_id = create_rate_plan()
        sim_cards = create_sim_cards(10)
        range_ = create_range(sim_cards)
        url = client.app.url_path_for("create_allocation")
        imsi_value = ***************
        allocation_data = create_allocation_data(
            ratePlanId=rate_plan_id, rangeId=range_.id, imsi=imsi_value
        )
        response = client.post(url, json=allocation_data)
        assert response.status_code == 201

    def test_range_does_not_exist(self, client, create_allocation_data):
        url = client.app.url_path_for("create_allocation")
        allocation_data = create_allocation_data()
        response = client.post(url, json=allocation_data)
        assert response.status_code == 400

    def test_range_not_ready(
        self, client, create_allocation_data, create_rate_plan, create_range
    ):
        range_ = create_range([])
        rate_plan_id = create_rate_plan()
        allocation_data = create_allocation_data(
            ratePlanId=rate_plan_id, rangeId=range_.id
        )
        url = client.app.url_path_for("create_allocation")
        response = client.post(url, json=allocation_data)
        assert response.status_code == 409

    def test_get_error_if_range_quantity_is_not_enough(
        self,
        client,
        create_allocation_data,
        create_rate_plan,
        create_sim_cards,
        create_range,
        create_allocation,
    ):
        sim_cards = create_sim_cards(1)
        range_ = create_range(sim_cards)
        rate_plan_id = create_rate_plan()
        create_allocation(rate_plan_id=rate_plan_id, range_id=range_.id, quantity=1)
        url = client.app.url_path_for("create_allocation")
        allocation_data = create_allocation_data(ratePlanId=rate_plan_id)
        response = client.post(url, json=allocation_data)
        assert response.status_code == 400


class TestDeleteAllocation:
    def test_happy_path_is_ok(
        self,
        client,
        create_rate_plan,
        create_sim_cards,
        create_range,
        create_allocation,
    ):
        sim_cards = create_sim_cards(1)
        range_ = create_range(sim_cards)
        rate_plan_id = create_rate_plan()
        allocation = create_allocation(
            rate_plan_id=rate_plan_id, range_id=range_.id, quantity=1
        )
        url = client.app.url_path_for("remove_allocation", id=allocation.id)
        response = client.delete(url)
        assert response.status_code == 204


class TestDeleteAllocations:
    def test_happy_path_is_ok(
        self,
        client,
        create_rate_plan,
        create_sim_cards,
        create_range,
        create_allocation,
    ):
        sim_cards = create_sim_cards(2)
        range_ = create_range(sim_cards)
        rate_plan_id = create_rate_plan()
        create_allocation(rate_plan_id=rate_plan_id, range_id=range_.id, quantity=1)
        create_allocation(rate_plan_id=rate_plan_id, range_id=range_.id, quantity=1)
        url = client.app.url_path_for("remove_allocations")
        url = f"{url}?range_id={range_.id}"
        response = client.delete(url)
        assert response.status_code == 204


class TestSimAuth:
    def test_get_sims_as_distributor_staff(
        self,
        authenticate,
        api_factory,
        create_distributor_staff_user,
        create_distributor_client_user,
        create_sim_range_file,
        upload_sim_range_file,
        create_rate_plan_api_factory,
        create_allocation_api_factory,
    ):

        distributor_client_user = create_distributor_client_user()
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        file = create_sim_range_file("***************", 10)
        response = upload_sim_range_file(file)
        range = response.json()
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id
        )
        rate_plan = response.json()
        create_allocation_api_factory(
            distributor_client_user.organization.account.id,
            range["id"],
            rate_plan["id"],
            range["quantity"],
        )
        response = api_factory("get_sims")
        assert len(response.json()["results"]) == 10

    def test_get_sims_as_distributor_client(
        self,
        authenticate,
        api_factory,
        create_distributor_staff_user,
        create_distributor_client_user,
        create_sim_range_file,
        upload_sim_range_file,
        create_rate_plan_api_factory,
        create_allocation_api_factory,
    ):
        distributor_client_user_1 = create_distributor_client_user(
            organization_id=1, account_id=1
        )
        distributor_client_user_2 = create_distributor_client_user(
            organization_id=2, account_id=2
        )
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        file = create_sim_range_file("***************", 100)
        response = upload_sim_range_file(file)
        range = response.json()
        response = create_rate_plan_api_factory(
            distributor_client_user_1.organization.account.id
        )
        rate_plan_1 = response.json()
        response = create_rate_plan_api_factory(
            distributor_client_user_2.organization.account.id
        )
        rate_plan_2 = response.json()
        quantity_1 = 10
        quantity_2 = 20

        create_allocation_api_factory(
            distributor_client_user_1.organization.account.id,
            range["id"],
            rate_plan_1["id"],
            quantity_1,
        )
        create_allocation_api_factory(
            distributor_client_user_2.organization.account.id,
            range["id"],
            rate_plan_2["id"],
            quantity_2,
        )

        authenticate(distributor_client_user_1)
        response = api_factory("get_sims")
        assert len(response.json()["results"]) == quantity_1

        authenticate(distributor_client_user_2)
        response = api_factory("get_sims")
        assert len(response.json()["results"]) == quantity_2


@pytest.fixture
def generate_allocated_sim_cards(
    api_factory,
    create_sim_range_file,
    upload_sim_range_file,
    create_rate_plan_api_factory,
    create_allocation_api_factory,
):
    def factory(account_id, imsi_first, length):
        file_ = create_sim_range_file(imsi_first, length)
        range_ = upload_sim_range_file(file_).json()
        rate_plan = create_rate_plan_api_factory(account_id).json()
        create_allocation_api_factory(
            account_id,
            range_["id"],
            rate_plan["id"],
            length,
        )

    return factory


class TestSimOrdering:
    def test_get_sims_ordering_with_wrong_order_field(self, api_factory):
        response = api_factory(
            "get_sims", request_kwargs={"params": {"ordering": "+msisdn"}}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_sims_ordering_without_pagination(
        self, api_factory, generate_allocated_sim_cards
    ):
        generate_allocated_sim_cards(
            account_id=1, imsi_first="***************", length=50
        )
        response = api_factory(
            "get_sims", request_kwargs={"params": {"ordering": "-msisdn"}}
        )
        records = response.json()["results"]
        assert records == sorted(records, key=lambda x: x["msisdn"], reverse=True)

    def test_get_sims_ordering_with_pagination(
        self, api_factory, generate_allocated_sim_cards
    ):

        generate_allocated_sim_cards(
            account_id=1, imsi_first="***************", length=250
        )
        page = 1
        page_size = 60
        all_sims = []
        while True:
            response = api_factory(
                "get_sims",
                request_kwargs={
                    "params": {
                        "ordering": "-iccid",
                        "page": page,
                        "page_size": page_size,
                    }
                },
            )
            response_data = response.json()
            all_sims.extend(response_data["results"])
            if response_data["lastPage"] == page:
                break
            page += 1
        assert all_sims == sorted(all_sims, key=lambda x: x["iccid"], reverse=True)


class TestSimSearching:
    def test_get_sims_correct_searching_fields_are_set(
        self, api_factory, create_sim_range_file, upload_sim_range_file
    ):
        file_ = create_sim_range_file("***************", 100)
        upload_sim_range_file(file_)
        response = api_factory(
            "get_sims", request_kwargs={"params": {"search": "test"}}
        )
        assert response.status_code == 200

    @pytest.mark.parametrize(
        "page_size, last_page, search_input",
        [(10, 1, "23458855879516"), (10, 4, "2345885587951"), (20, 2, "2345885587951")],
    )
    def test_get_sims_correct_pagination_response_received(
        self,
        api_factory,
        create_sim_range_file,
        upload_sim_range_file,
        page_size,
        last_page,
        search_input,
    ):
        file_ = create_sim_range_file("***************", 100)
        upload_sim_range_file(file_)
        response = api_factory(
            "get_sims",
            request_kwargs={"params": {"search": search_input, "page_size": page_size}},
        )
        body = response.json()
        assert body["lastPage"] == last_page


class TestReallocation:
    def setup_method(self):
        self.authorization_service = MagicMock(spec=AbstractAuthorizationAPI)

    def test_re_allocation_process_success(
        self, request_mock, create_reallocation_audit_fixture
    ):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock()
        sim_service_mock.re_allocation_validation.return_value = (
            create_reallocation_audit_fixture,
            [2],
        )

        imsi_list = ReAllocation(imsi=["***************", "***************"])
        response = re_allocation_process(
            request=request_mock,
            account_id=1,
            rate_plan_id=1,
            imsi_list=imsi_list,
            background_tasks=BackgroundTasks(),
            authorization=self.authorization_service,
            sim_service=sim_service_mock,
        )

        assert response == {
            "message": "1 IMSIs will be re-allocated and 0 "
            "IMSIs already assigned to choosen Rate Plan."
        }

    def test_re_allocation_process_exception(self, request_mock):
        sim_service_mock = MagicMock()

        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock.re_allocation_validation.side_effect = (
            exceptions.RatePlanNotFound("Rate Plan not found")
        )
        imsi_list = ReAllocation(imsi=["***************", "***************"])
        with pytest.raises(HTTPException) as exc_info:
            re_allocation_process(
                request=request_mock,
                account_id=1,
                rate_plan_id=1,
                imsi_list=imsi_list,
                background_tasks=BackgroundTasks(),
                authorization=self.authorization_service,
                sim_service=sim_service_mock,
            )
        assert exc_info.value.status_code == 404
        assert str(exc_info.value.detail) == "Rate Plan not found"

    def test_re_allocation_process_invalid_imsi(self, request_mock):
        sim_service_mock = MagicMock()
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock.re_allocation_validation.side_effect = (
            exceptions.IMSINotAvailableForAllocation(
                "IMSI not available for reallocation"
            )
        )
        imsi_list = ReAllocation(imsi=["***************", "***************"])
        with pytest.raises(HTTPException) as exc_info:
            re_allocation_process(
                request=request_mock,
                account_id=1,
                rate_plan_id=1,
                imsi_list=imsi_list,
                background_tasks=BackgroundTasks(),
                authorization=self.authorization_service,
                sim_service=sim_service_mock,
            )
        assert exc_info.value.status_code == 400
        assert str(exc_info.value.detail) == "IMSI not available for reallocation"

    def test_re_allocation_validation_success(
        self, request_mock, create_reallocation_audit_fixture
    ):
        sim_service_mock = MagicMock()
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock.re_allocation_validation.return_value = (
            create_reallocation_audit_fixture,
            [2],
        )

        re_allocation_process(
            request=request_mock,
            account_id=1,
            rate_plan_id=1,
            imsi_list=ReAllocation(imsi=["***************", "***************"]),
            background_tasks=BackgroundTasks(),
            authorization=self.authorization_service,
            sim_service=sim_service_mock,
        )
        sim_service_mock.re_allocation_validation.assert_called_once_with(
            account_id=1,
            rate_plan_id=1,
            imsi_list=["***************", "***************"],
        )


class TestSimService:
    def setup_method(self):
        self.authorization_service = MagicMock(spec=AbstractAuthorizationAPI)

    def test_connection_history_export_success(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)

        mock_cdr_history_data = {
            "iccid": "8944538532046590001",
            "imsi": "***************",
            "country": "USA",
            "carrier": "GBRME",
            "session_starttime": "2023-03-11 21:13:44",
            "session_endtime": "2023-03-11 21:13:44",
            "duration": 40,
            "data_volume": 9431,
            "country_name": "United States",
            "carrier_name": "EE",
        }
        mock_cdr_history = SimCDRHistory(**mock_cdr_history_data)

        sim_service_mock.connection_history_export.return_value = [mock_cdr_history]

        imsi = "***************"
        month = "2023-03"
        response = list(
            connection_history_export(
                imsi=imsi,
                month=month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )
        )

        assert len(response) == 1
        assert response[0] == mock_cdr_history
        assert isinstance(response, list)
        assert isinstance(response[0], SimCDRHistory)

    def test_no_connection_history(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.connection_history_export.side_effect = NoConnectionHistory(
            mock_imsi, mock_month
        )

        with pytest.raises(HTTPException) as e:
            connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert (
            e.value.detail == f"No history found for IMSI {mock_imsi} "
            f"on the specified date {mock_month}."
        )

    def test_connection_history_export_value_error(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.connection_history_export.side_effect = ValueError(
            "We are unable to process your request."
        )

        with pytest.raises(HTTPException) as e:
            connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 400
        assert e.value.detail == "We are unable to process your request."

    def test_connection_history_export_imsi_does_not_exist(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.connection_history_export.side_effect = IMSIDoesNotExit(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert e.value.detail == f"IMSI {mock_imsi} does not exist."

    def test_connection_history_export_imsi_not_allocated(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.connection_history_export.side_effect = IMSINotAllocated(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 401
        assert (
            e.value.detail
            == f"Requested imsi {mock_imsi} not allocated to the requested account."
        )

    def test_voice_connection_history_export_success(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)

        mock_cdr_history_data = {
            "imsi": "***************",
            "iccid": "8944538532046590001",
            "country": "USA",
            "carrier": "GBRME",
            "call_number": "************",
            "call_date": "2023-03-10 08:39:47",
            "call_minutes": "85",
            "country_name": "United States",
            "carrier_name": "EE",
        }
        mock_cdr_history = SimVoiceCDRHistory(**mock_cdr_history_data)

        sim_service_mock.voice_connection_history_export.return_value = [
            mock_cdr_history
        ]

        imsi = "***************"
        month = "2023-03"
        response = list(
            voice_connection_history_export(
                imsi=imsi,
                month=month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )
        )

        assert len(response) == 1
        assert response[0] == mock_cdr_history
        assert isinstance(response, list)
        assert isinstance(response[0], SimVoiceCDRHistory)

    def test_no_voice_connection_history(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.voice_connection_history_export.side_effect = (
            NoConnectionHistory(mock_imsi, mock_month)
        )

        with pytest.raises(HTTPException) as e:
            voice_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert (
            e.value.detail == f"No history found for IMSI {mock_imsi} "
            f"on the specified date {mock_month}."
        )

    def test_voice_connection_history_export_value_error(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.voice_connection_history_export.side_effect = ValueError(
            "We are unable to process your request."
        )

        with pytest.raises(HTTPException) as e:
            voice_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 400
        assert e.value.detail == "We are unable to process your request."

    def test_voice_connection_history_export_imsi_does_not_exist(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.voice_connection_history_export.side_effect = IMSIDoesNotExit(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            voice_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert e.value.detail == f"IMSI {mock_imsi} does not exist."

    def test_voice_connection_history_export_imsi_not_allocated(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.voice_connection_history_export.side_effect = IMSINotAllocated(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            voice_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 401
        assert (
            e.value.detail
            == f"Requested imsi {mock_imsi} not allocated to the requested account."
        )

    def test_sms_connection_history_export_success(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)

        mock_cdr_history_data = {
            "iccid": "8944538531005850000",
            "imsi": "***************",
            "country": "GBR",
            "carrier": "GBRME",
            "date_sent": "2023-04-05T18:47:05",
            "sent_from": "*********",
            "sent_to": "*********",
            "country_name": "United Kingdom",
            "carrier_name": "EE",
        }
        mock_cdr_history = SimSMSCDRHistory(**mock_cdr_history_data)

        sim_service_mock.sms_connection_history_export.return_value = [mock_cdr_history]

        imsi = "***************"
        month = "2023-03"
        response = list(
            sms_connection_history_export(
                imsi=imsi,
                month=month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )
        )

        assert len(response) == 1
        assert response[0] == mock_cdr_history
        assert isinstance(response, list)
        assert isinstance(response[0], SimSMSCDRHistory)

    def test_no_sms_connection_history(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.sms_connection_history_export.side_effect = (
            NoConnectionHistory(mock_imsi, mock_month)
        )

        with pytest.raises(HTTPException) as e:
            sms_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert (
            e.value.detail == f"No history found for IMSI {mock_imsi} "
            f"on the specified date {mock_month}."
        )

    def test_sms_connection_history_export_value_error(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.sms_connection_history_export.side_effect = ValueError(
            "We are unable to process your request."
        )

        with pytest.raises(HTTPException) as e:
            sms_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 400
        assert e.value.detail == "We are unable to process your request."

    def test_sms_connection_history_export_imsi_does_not_exist(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.sms_connection_history_export.side_effect = IMSIDoesNotExit(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            sms_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 404
        assert e.value.detail == f"IMSI {mock_imsi} does not exist."

    def test_sms_connection_history_export_imsi_not_allocated(self, request_mock):
        self.authorization_service.is_authorized.return_value = "PERMIT"
        sim_service_mock = MagicMock(spec=AbstractSimService)
        mock_imsi = "234588560660000"
        mock_month = "2023-03"
        sim_service_mock.sms_connection_history_export.side_effect = IMSINotAllocated(
            mock_imsi
        )

        with pytest.raises(HTTPException) as e:
            sms_connection_history_export(
                imsi=mock_imsi,
                month=mock_month,
                request=request_mock,
                sim_service=sim_service_mock,
                authorization=self.authorization_service,
            )

        assert e.value.status_code == 401
        assert (
            e.value.detail
            == f"Requested imsi {mock_imsi} not allocated to the requested account."
        )


class TestSimRatePlanChange:
    def setup_method(self):
        self.authorization_service = MagicMock(spec=AbstractAuthorizationAPI)

    def test_validate_rate_plan_change_process_success(
        self, request_mock, rate_plan_change_audit_fixture
    ):
        self.authorization_service.is_authorized.return_value = "PERMIT"

        sim_service_mock = MagicMock()
        sim_service_mock.rate_plan_change_sim_validation.return_value = (
            rate_plan_change_audit_fixture
        )

        imsi = "***************"
        response = validate_rate_plan_change(
            request=request_mock,
            account_id=1,
            rate_plan_id=1,
            imsi=imsi,
            authorization=self.authorization_service,
            sim_service=sim_service_mock,
        )

        assert response == rate_plan_change_audit_fixture

    def test_validate_rate_plan_change_process_for_sim_limit_success(
        self, request_mock, valid_rate_plan_change_audit_fixture
    ):
        self.authorization_service.is_authorized.return_value = "PERMIT"

        sim_service_mock = MagicMock()
        sim_service_mock.rate_plan_change_sim_validation.return_value = (
            valid_rate_plan_change_audit_fixture
        )

        imsi = "***************"
        response = validate_rate_plan_change(
            request=request_mock,
            account_id=1,
            rate_plan_id=1,
            imsi=imsi,
            authorization=self.authorization_service,
            sim_service=sim_service_mock,
        )

        assert response == valid_rate_plan_change_audit_fixture

    def test_validate_rate_plan_change_rate_plan_account_mapping_error(
        self, mock_dependencies
    ):
        error_exception = exceptions.RatePlanAccountMappingError(
            "Account mapping issue"
        )
        mock_dependencies[
            "sim_service_mock"
        ].rate_plan_change_sim_validation.side_effect = error_exception

        with pytest.raises(HTTPException) as exc_info:
            validate_rate_plan_change(
                request=mock_dependencies["request_mock"],
                account_id=1,
                rate_plan_id=1,
                imsi="***************",
                authorization=mock_dependencies["authorization_mock"],
                sim_service=mock_dependencies["sim_service_mock"],
            )

        assert exc_info.value.status_code == 400
        assert str(exc_info.value.detail) == "Account mapping issue"

    def test_validate_rate_plan_change_rate_plan_not_found(self, mock_dependencies):
        mock_dependencies[
            "sim_service_mock"
        ].rate_plan_change_sim_validation.side_effect = exceptions.RatePlanNotFound(
            "Rate plan not found"
        )

        with pytest.raises(HTTPException) as exc_info:
            validate_rate_plan_change(
                request=mock_dependencies["request_mock"],
                account_id=1,
                rate_plan_id=1,
                imsi="***************",
                authorization=mock_dependencies["authorization_mock"],
                sim_service=mock_dependencies["sim_service_mock"],
            )

        assert exc_info.value.status_code == 404
        assert str(exc_info.value.detail) == "Rate plan not found"

    def test_validate_rate_plan_change_rate_plan_exception(self, mock_dependencies):
        mock_dependencies[
            "sim_service_mock"
        ].rate_plan_change_sim_validation.side_effect = exceptions.RatePlanException(
            "Rate plan conflict"
        )

        with pytest.raises(HTTPException) as exc_info:
            validate_rate_plan_change(
                request=mock_dependencies["request_mock"],
                account_id=1,
                rate_plan_id=1,
                imsi="***************",
                authorization=mock_dependencies["authorization_mock"],
                sim_service=mock_dependencies["sim_service_mock"],
            )

        assert exc_info.value.status_code == 409
        assert str(exc_info.value.detail) == "Rate plan conflict"

    def test_validate_rate_plan_change_unexpected_exception(self, mock_dependencies):
        mock_dependencies[
            "sim_service_mock"
        ].rate_plan_change_sim_validation.side_effect = Exception("Unexpected error")

        with pytest.raises(HTTPException) as exc_info:
            validate_rate_plan_change(
                request=mock_dependencies["request_mock"],
                account_id=1,
                rate_plan_id=1,
                imsi="***************",
                authorization=mock_dependencies["authorization_mock"],
                sim_service=mock_dependencies["sim_service_mock"],
            )

        assert exc_info.value.status_code == 500
        assert str(exc_info.value.detail) == "Couldn't process the request"


class TestMSISDN:
    def test_get_available_msisdn_count_success(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_available_msisdn_count.return_value = (
            model.MsisdnCountDetails(total_count=100, national=40, international=60)
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/count")
        mock_request.method = "GET"

        response = get_available_msisdn_count(
            request=mock_request,
            sim_service=mock_sim_service,
            authorization=mock_auth,
            trace_id=uuid4(),
        )

        assert response.total_count == 100
        assert response.national == 40
        assert response.international == 60

    def test_get_available_msisdn_count_failure(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_available_msisdn_count.side_effect = Exception(
            "Unexpected error"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/count")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_available_msisdn_count(
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert str(exc_info.value.detail) == "Couldn't process the request"

    def test_get_msisdn_export_success(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_msisdn_data = [
            MagicMock(
                id=1,
                msisdn="*********0",
                imsi="52634*********0",
                created_at="2023-03-11 21:13:44",
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn_factor=model.MSISDNFactor.NATIONAL,
                uploaded_by="User1",
                account_name="Account1",
                logo_key=None,
                logo_url=None,
            )
        ]
        mock_sim_service.get_msisdn_export.return_value = mock_msisdn_data
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/export")
        mock_request.method = "GET"

        response = list(
            get_msisdn_export(
                request=mock_request,
                sim_service=mock_sim_service,
                searching=None,
                authorization=mock_auth,
                trace_id=uuid4(),
            )
        )

        assert len(response) == 1
        assert isinstance(response, list)

        assert isinstance(response[0], MsisdnPoolExport)
        assert response[0].msisdn == "*********0"

    def test_get_msisdn_export_failure(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_export.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/export")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            list(
                get_msisdn_export(
                    request=mock_request,
                    sim_service=mock_sim_service,
                    searching=None,
                    authorization=mock_auth,
                    trace_id=uuid4(),
                )
            )

        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Couldn't process the request"

    def test_get_available_msisdn_count_not_found(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_available_msisdn_count.side_effect = NotFound(
            "No MSISDNs available"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/count")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_available_msisdn_count(
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert str(exc_info.value.detail) == "No MSISDNs available"

    def test_get_available_msisdn_count_value_error(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_available_msisdn_count.side_effect = ValueError(
            "Invalid count"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/count")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_available_msisdn_count(
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert str(exc_info.value.detail) == "Invalid count"

    def test_get_msisdn_export_not_found(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_export.side_effect = NotFound(
            "No MSISDNs available"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/export")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            list(
                get_msisdn_export(
                    request=mock_request,
                    sim_service=mock_sim_service,
                    searching=None,
                    authorization=mock_auth,
                    trace_id=uuid4(),
                )
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert str(exc_info.value.detail) == "No MSISDNs available"

    def test_get_msisdn_export_value_error(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_export.side_effect = ValueError(
            "Invalid search parameter"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/export")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            list(
                get_msisdn_export(
                    request=mock_request,
                    sim_service=mock_sim_service,
                    searching=None,
                    authorization=mock_auth,
                    trace_id=uuid4(),
                )
            )

        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert str(exc_info.value.detail) == "Invalid search parameter"

    def test_get_msisdn_export_unexpected_error(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_export.side_effect = Exception("Unexpected error")
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/export")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            list(
                get_msisdn_export(
                    request=mock_request,
                    sim_service=mock_sim_service,
                    searching=None,
                    authorization=mock_auth,
                    trace_id=uuid4(),
                )
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert str(exc_info.value.detail) == "Couldn't process the request"

    def test_get_msisdn_pool_details_success(self, api_security):

        mock_sim_service = MagicMock()
        mock_pagination = MagicMock()
        mock_searching = MagicMock()

        mock_pagination.page = 1
        mock_pagination.page_size = 2
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/records")
        mock_request.method = "GET"

        mock_msisdn_data = [
            model.MsisdnDetails(
                id=1,
                msisdn="*********0",
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                created_at=datetime(2023, 3, 11, 21, 13, 44),
                uploaded_by="User1",
                account_name="Account1",
                logo_key="logo123",
                logo_url="http://example.com/logo1.png",
            ),
            model.MsisdnDetails(
                id=2,
                msisdn="**********",
                sim_profile=model.SimProfile.VOICE_SMS_DATA,
                msisdn_factor=model.MSISDNFactor.NATIONAL,
                created_at=datetime(2023, 5, 15, 10, 5, 30),
                uploaded_by="User2",
                account_name="Account2",
                logo_key="logo456",
                logo_url="http://example.com/logo2.png",
            ),
        ]

        total_count = 5

        mock_sim_service.get_msisdn_pool_details.return_value = (
            mock_msisdn_data,
            total_count,
        )

        response = get_msisdn_pool_details(
            request=mock_request,
            sim_service=mock_sim_service,
            pagination=mock_pagination,
            searching=mock_searching,
            authorization=mock_auth,
            trace_id=uuid4(),
        )

        assert isinstance(response, PaginatedResponse)
        assert response.total_count == 5
        assert response.page == 1
        assert response.page_size == 2
        assert response.last_page == math.ceil(total_count / mock_pagination.page_size)
        assert len(response.results) == 2
        assert response.results[0].msisdn == "*********0"
        assert response.results[1].msisdn == "**********"

    def test_get_msisdn_pool_details_not_found(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_pool_details.side_effect = NotFound(
            "No MSISDNs found"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/records")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_msisdn_pool_details(
                request=mock_request,
                sim_service=mock_sim_service,
                pagination=MagicMock(spec=Pagination),
                searching=MagicMock(spec=Searching),
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "No Msisdn found."

    def test_get_msisdn_pool_details_unexpected_error(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.get_msisdn_pool_details.side_effect = Exception(
            "Unexpected error"
        )
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/records")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_msisdn_pool_details(
                request=mock_request,
                sim_service=mock_sim_service,
                pagination=MagicMock(spec=Pagination),
                searching=MagicMock(spec=Searching),
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert exc_info.value.detail == "Couldn't process you request."

    def test_get_msisdn_factor_success(self):
        mock_sim_service = MagicMock()
        mock_sim_service.get_msisdn_factor.return_value = "*********0"

        response = get_msisdn_factor(
            msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert response == {"result": "*********0"}

    def test_get_msisdn_factor_not_found(self):
        mock_sim_service = MagicMock()
        mock_sim_service.get_msisdn_factor.side_effect = NotFound("MSISDN not found")

        with pytest.raises(HTTPException) as exc_info:
            get_msisdn_factor(
                msisdn_factor=model.MSISDNFactor.NATIONAL,
                sim_service=mock_sim_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert str(exc_info.value.detail) == "MSISDN not found"

    def test_get_msisdn_factor_unexpected_error(self):
        mock_sim_service = MagicMock()
        mock_sim_service.get_msisdn_factor.side_effect = Exception("Unexpected error")

        with pytest.raises(HTTPException) as exc_info:
            get_msisdn_factor(
                msisdn_factor=model.MSISDNFactor.INTERNATIONAL,
                sim_service=mock_sim_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert str(exc_info.value.detail) == "Couldn't process you request."

    def test_update_sim_card_details_success(self, api_security):
        mock_sim_service = MagicMock()

        # Simulate a valid response model
        mock_response = MagicMock()
        mock_response.imsi = "***************"
        mock_response.msisdn = "*********0"
        mock_response.sim_profile = model.SimProfile.DATA_ONLY  # Use Enum directly

        # Mock the method return value
        mock_sim_service.update_sim_card_details_by_imsi.return_value = mock_response

        # Ensure the conversion method works as expected
        model.UpdateSimCardDetailsResult.from_response_model = MagicMock(
            return_value=model.UpdateSimCardDetailsResult(
                imsi=mock_response.imsi,
                msisdn=mock_response.msisdn,
                sim_profile=mock_response.sim_profile,
            )
        )

        mock_auth, mock_request = api_security(
            "/v1/glass/sim/imsi/{imsi}/profile/{sim_profile}/msisdn/{msisdn}"
        )
        mock_request.method = "PATCH"

        response = update_sim_card_details(
            imsi="***************",
            sim_profile=model.SimProfile.DATA_ONLY,
            msisdn="*********0",
            request=mock_request,
            sim_service=mock_sim_service,
            authorization=mock_auth,
            trace_id=uuid4(),
        )

        assert response.imsi == "***************"
        assert response.msisdn == "*********0"
        assert response.sim_profile == model.SimProfile.DATA_ONLY

    @pytest.mark.parametrize(
        "exception, status_code, detail",
        [
            (exceptions.MSISDNNotFound("MSISDN not found"), 404, "MSISDN not found"),
            (exceptions.IMSIError("IMSI error"), 400, "IMSI error"),
            (exceptions.AlreadyExist("Already exists"), 409, "Already exists"),
            # (Unauthorized("Unauthorized access"), 401, "Unauthorized access."),
        ],
    )
    def test_update_sim_card_details_exceptions(
        self, api_security, exception, status_code, detail
    ):
        mock_sim_service = MagicMock()
        mock_sim_service.update_sim_card_details_by_imsi.side_effect = exception

        mock_auth, mock_request = api_security(
            "/v1/glass/sim/imsi/{imsi}/profile/{sim_profile}/msisdn/{msisdn}"
        )
        mock_request.method = "PATCH"

        with pytest.raises(HTTPException) as exc_info:
            update_sim_card_details(
                imsi="***************",
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn="*********0",
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status_code
        assert str(exc_info.value.detail) == detail

    def test_update_sim_card_details_unexpected_error(self, api_security):
        mock_sim_service = MagicMock()
        mock_sim_service.update_sim_card_details_by_imsi.side_effect = Exception(
            "Unexpected error"
        )
        mock_auth, mock_request = api_security(
            "/v1/glass/sim/imsi/{imsi}/profile/{sim_profile}/msisdn/{msisdn}"
        )
        mock_request.method = "PATCH"

        with pytest.raises(HTTPException) as exc_info:
            update_sim_card_details(
                imsi="***************",
                sim_profile=model.SimProfile.DATA_ONLY,
                msisdn="*********0",
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert str(exc_info.value.detail) == "Couldn't process you request."

    @patch("src.sim.parser.settings.MAX_FILE_SIZE_MB", 10)
    def test_upload_msisdn_success(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test_msisdn.csv"
        mock_file.file = BytesIO(b"MSISDN\n*********0\n9876543210\n")
        mock_file.content_type = "text/csv"

        mock_sim_service.upload_msisdn.return_value = MagicMock(
            total_msisdn=2, error_msisdn=0, error_result=[]
        )

        response = upload_msisdn(
            request=mock_request,
            file=mock_file,
            sim_service=mock_sim_service,
            cdr_storage=mock_cdr_storage,
            authorization=mock_auth,
            trace_id=uuid4(),
        )

        assert response.total_msisdn == 2

    def test_upload_msisdn_parsing_error(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test_msisdn.csv"
        mock_file.file = BytesIO(b"invalid_data")
        mock_file.content_type = "text/csv"

        with pytest.raises(HTTPException) as exc_info:
            upload_msisdn(
                request=mock_request,
                file=mock_file,
                sim_service=mock_sim_service,
                cdr_storage=mock_cdr_storage,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    @patch("src.sim.parser.settings.MAX_FILE_SIZE_MB", 10)
    def test_upload_msisdn_invalid_file_format(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "invalid_file.pdf"
        mock_file.file = BytesIO(b"Some binary content")
        mock_file.content_type = "application/pdf"

        with pytest.raises(HTTPException) as exc_info:
            upload_msisdn(
                request=mock_request,
                file=mock_file,
                sim_service=mock_sim_service,
                cdr_storage=mock_cdr_storage,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == 400
        assert "Invalid file format. Please upload a CSV file." in exc_info.value.detail

    @patch("src.sim.parser.settings.MAX_FILE_SIZE_MB", 1)
    def test_upload_msisdn_large_file(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()

        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "large_file.csv"
        mock_file.file = BytesIO(b"1" * 1024 * 1024 * 2)
        mock_file.content_type = "text/csv"

        with pytest.raises(HTTPException) as exc_info:
            upload_msisdn(
                request=mock_request,
                file=mock_file,
                sim_service=mock_sim_service,
                cdr_storage=mock_cdr_storage,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == 400
        assert "File size exceeds the maximum limit of 1MB." in exc_info.value.detail

    @patch("src.sim.parser.settings.MAX_FILE_SIZE_MB", 10)
    def test_upload_msisdn_invalid_msisdn_format(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()

        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        # Mock a file with invalid MSISDN format
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "invalid_msisdn.csv"
        mock_file.file = BytesIO(b"invalidmsisdn\n1234abcd567")
        mock_file.content_type = "text/csv"

        mock_sim_service.upload_msisdn.side_effect = ParsingError(
            "CSV file must contain only the 'MSISDN' column."
        )

        with pytest.raises(HTTPException) as exc_info:
            upload_msisdn(
                request=mock_request,
                file=mock_file,
                sim_service=mock_sim_service,
                cdr_storage=mock_cdr_storage,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == 400
        assert (
            "CSV file must contain only the 'MSISDN' column." in exc_info.value.detail
        )

    @patch("src.sim.parser.settings.MAX_FILE_SIZE_MB", 10)
    def test_upload_msisdn_internal_server_error(self, api_security):
        mock_sim_service = MagicMock()
        mock_cdr_storage = MagicMock()

        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn/documents")
        mock_request.method = "POST"

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "valid_msisdn.csv"
        mock_file.file = BytesIO(b"MSISDN\n*********0\n9876543210\n")
        mock_file.content_type = "text/csv"

        mock_sim_service.upload_msisdn.side_effect = Exception("Unexpected error")

        with pytest.raises(HTTPException) as exc_info:
            upload_msisdn(
                request=mock_request,
                file=mock_file,
                sim_service=mock_sim_service,
                cdr_storage=mock_cdr_storage,
                authorization=mock_auth,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == 500
        assert "Couldn't process the request" in exc_info.value.detail

    def test_update_selected_sim_details_success(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_auth, mock_request = api_security("/v1/glass/sim/msisdn")
        mock_request.method = "PUT"
        mock_trace_id = uuid4()
        mock_background_tasks = MagicMock(spec=BackgroundTasks)

        update_request = BulkUpdateRequest(
            sim_profile="DATA_ONLY",
            msisdn_factor="INTERNATIONAL",
            msisdn_map=[
                MSISDNMapping(
                    IMSI="***************",
                    MSISDN="*********0",
                ),
            ],
        )

        response = update_selected_sim_details(
            request=mock_request,
            update_request=update_request,
            background_tasks=mock_background_tasks,
            sim_service=mock_sim_service,
            authorization=mock_auth,
            trace_id=mock_trace_id,
        )

        assert response == {"message": "We have received your request."}
        mock_background_tasks.add_task.assert_called_once()


class TestUnallocation:
    def setup_method(self):
        self.simcard = SimService(
            AbstractSimRepository,
            AbstractRatePlanRepository,
            AbstractCdrRepository,
            AbstractSIMProvisioningAPI,
            AbstractMarketShareAPI,
            AbstractAuditService,
            MediaService,
            HTTPRedisAPI,
        )

    def test_unallocation_success(self):

        unallocation_reponse = model.UnallocateSimCardDetails(
            message="1 out of 1 IMSIs were unallocated"
        )
        self.simcard.unallocate_sim_cards = MagicMock(return_value=unallocation_reponse)
        result = unallocate_imsis(
            unallocate_imsis=model.Unallocation(imsis=["***************"]),
            sim_service=self.simcard,
            trace_id=uuid4(),
        )
        assert isinstance(result, UnallocateSimCardDetails)

    def test_unallocation_with_not_found_imsis(self):
        unallocation_response = model.UnallocateSimCardDetails(
            message="0 out of 1 IMSIs were unallocated"
        )
        self.simcard.unallocate_sim_cards = MagicMock(
            return_value=unallocation_response
        )

        result = unallocate_imsis(
            unallocate_imsis=model.Unallocation(imsis=["999999999999999"]),
            sim_service=self.simcard,
            trace_id=uuid4(),
        )
        assert isinstance(result, UnallocateSimCardDetails)
        assert result.message == "0 out of 1 IMSIs were unallocated"

    def test_unallocation_already_unallocated(self):
        unallocation_response = model.UnallocateSimCardDetails(
            message="0 out of 2 IMSIs were unallocated"
        )
        self.simcard.unallocate_sim_cards = MagicMock(
            return_value=unallocation_response
        )

        result = unallocate_imsis(
            unallocate_imsis=model.Unallocation(
                imsis=["111111111111111", "111111111111122"]
            ),
            sim_service=self.simcard,
            trace_id=uuid4(),
        )
        assert isinstance(result, UnallocateSimCardDetails)
        assert result.message == "0 out of 2 IMSIs were unallocated"

    def test_unallocation_raises_unallocation_exception(self):
        self.simcard.unallocate_sim_cards = MagicMock(
            side_effect=UnallocationException("SIM card could not be unallocated.")
        )

        with pytest.raises(HTTPException) as exc_info:
            unallocate_imsis(
                unallocate_imsis=model.Unallocation(imsis=["111111111111999"]),
                sim_service=self.simcard,
                trace_id=uuid4(),
            )
        assert exc_info.value.status_code == 409

    def test_unallocation_raises_generic_exception(self):
        self.simcard.unallocate_sim_cards = MagicMock(
            side_effect=Exception("Some internal error")
        )

        with pytest.raises(HTTPException) as exc_info:
            unallocate_imsis(
                unallocate_imsis=model.Unallocation(imsis=["111111111111888"]),
                sim_service=self.simcard,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "We couldn't process your request."


class TestDeleteIMSIs:
    def test_delete_imsis_success(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.imsis_to_delete.return_value = model.IMSIDeleteResponse(
            message="1 out of 3 IMSIs were deleted."
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/imsi")
        mock_request.method = "DELETE"
        sim_cards = model.ISMIToDelete(imsis=[***************, 600000000000002])

        response = delete_imsis(
            request=mock_request,
            sim_cards=sim_cards,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, IMSIDeleteResponse)
        assert response.message == "1 out of 3 IMSIs were deleted."

    def test_delete_imsis_generic_exception(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.imsis_to_delete.side_effect = Exception("Something went wrong")

        mock_auth, mock_request = api_security("/v1/glass/sim/imsi")
        mock_request.method = "DELETE"
        sim_cards = model.ISMIToDelete(imsis=[***************])

        with pytest.raises(HTTPException) as exc_info:
            delete_imsis(
                request=mock_request,
                sim_cards=sim_cards,
                sim_service=mock_sim_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process you request."


class TestCreateOrder:
    def test_create_order_success(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Test Customer",
                customer_account_name="Test Account",
                customer_account_id=1,
                customer_email="<EMAIL>",
                customer_contact_no="*********0",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test Street",
                city="Test City",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=50),
            ],
            notes="Test order notes",
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        assert response.message == "Order created successfully"
        assert response.uuid is not None
        mock_sim_service.create_order.assert_called_once()

    def test_create_order_with_minimal_data(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Minimal Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Jane Doe", address_line1="456 Minimal Street"
            ),
            order_items=[model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=25)],
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        assert response.message == "Order created successfully"
        mock_sim_service.create_order.assert_called_once()

    def test_create_order_parsing_error(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.side_effect = ParsingError("Invalid order data")

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Error Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Error Contact", address_line1="Error Street"
            ),
            order_items=[model.OrderItem(sim_type="Invalid SIM", quantity=0)],
        )

        with pytest.raises(HTTPException) as exc_info:
            create_order(
                request=mock_request,
                order=order_request,
                sim_service=mock_sim_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Invalid order data"

    def test_create_order_generic_exception(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.side_effect = Exception(
            "Database connection error"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Exception Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Exception Contact", address_line1="Exception Street"
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
        )

        with pytest.raises(HTTPException) as exc_info:
            create_order(
                request=mock_request,
                order=order_request,
                sim_service=mock_sim_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Couldn't process the order request" in exc_info.value.detail

    def test_create_order_empty_order_items(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Empty Items Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Empty Items Contact", address_line1="Empty Items Street"
            ),
            order_items=[],
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        mock_sim_service.create_order.assert_called_once()

    def test_create_order_multiple_order_items(self, api_security):
        mock_sim_service = MagicMock(spec=AbstractSimService)
        mock_sim_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/sim/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Multi Items Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Multi Items Contact", address_line1="Multi Items Street"
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=500),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=300),
                model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=200),
            ],
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            sim_service=mock_sim_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        mock_sim_service.create_order.assert_called_once()

        # Verify the order model conversion
        call_args = mock_sim_service.create_order.call_args[1]["order"]
        assert len(call_args.order_items) == 3
        assert call_args.customer_details.customer_name == "Multi Items Customer"
