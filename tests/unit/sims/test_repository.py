from collections import namedtuple
from dataclasses import dataclass
from datetime import datetime
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from sqlalchemy import func, literal_column, select
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm.session import Session

from common.types import IMSI
from common.utils import trace_id_var
from sim.adapters.repository import DatabaseSimRepository, InMemorySimRepository
from sim.domain import model


@pytest.fixture
def database_sim_repository_mock():
    def auth_factory(session_mock):
        mock_database_sim_repository = DatabaseSimRepository(session=session_mock)

        return mock_database_sim_repository

    return auth_factory


@pytest.fixture
def session_mock():
    mock_session = MagicMock(spec=Session)
    return mock_session


@pytest.fixture
def sim_repository(session_mock):
    return DatabaseSimRepository(session=session_mock)


@dataclass
class MockMsisdnDetailsResponse:
    msisdn: model.MSISDN
    sim_profile: str
    created_at: datetime
    uploaded_by: str
    imsi: IMSI | None = None
    country: str | None = None
    sim_provider: str | None = None
    account_name: str | None = None
    logo_key: str | None = None
    logo_url: str | None = None


class TestSimRepository:
    @pytest.fixture
    def repository(self):
        return InMemorySimRepository()

    def test_check_imsi_account_success(self, repository):
        account_id = 1
        imsi = ["***************"]
        response = repository.check_imsi_account(account_id, imsi)
        assert response == 1

    def test_check_imsi_account_none(self, repository):
        account_id = 1
        imsi = "***************"
        response = repository.check_imsi_account(account_id, imsi)
        expected_response = None
        assert response == expected_response

    def test_get_available_msisdn_count(self, sim_repository, session_mock):
        mock_response = MagicMock()
        mock_response.total_count = 100
        mock_response.national_count = 40
        mock_response.international_count = 60

        session_mock.execute.return_value.one.return_value = mock_response

        result = sim_repository.get_available_msisdn_count()

        assert isinstance(result, model.MsisdnCountDetails)
        assert result.total_count == 100
        assert result.national == 40
        assert result.international == 60

    def test_get_available_msisdn_count_invalid_response(
        self, sim_repository, session_mock
    ):

        session_mock.execute.return_value.one.return_value = None

        with pytest.raises(AttributeError):
            sim_repository.get_available_msisdn_count()

    def test_get_msisdn_export(self, sim_repository, session_mock):
        mock_results = {
            "msisdn": "**********",
            "sim_profile": model.SimProfile.DATA_ONLY,
            "msisdn_factor": model.MSISDNFactor.NATIONAL,
            "created_at": "2023-03-11 21:13:44",
            "uploaded_by": "User1",
        }

        result_model = model.MsisdnPool(**mock_results)

        mock_execute_result = MagicMock()
        mock_execute_result.all.return_value = [result_model]
        session_mock.execute.return_value = mock_execute_result

        results = list(sim_repository.get_msisdn_export())

        assert len(results) == 1
        assert isinstance(results[0], model.MsisdnDetails)
        assert results[0].msisdn == "**********"
        assert results[0].sim_profile == model.SimProfile.DATA_ONLY

    def test_get_msisdn_export_empty_result(self, sim_repository, session_mock):

        session_mock.execute.all.return_value = []

        results = list(sim_repository.get_msisdn_export())

        assert results == []

    def test_get_msisdn_export_none_result(self, sim_repository, session_mock):

        mock_execute_result = MagicMock()
        mock_execute_result.all.return_value = []
        session_mock.execute.return_value = mock_execute_result

        result = list(sim_repository.get_msisdn_export())

        assert result == []

    def test_get_msisdn_pool_details(self, sim_repository, session_mock):

        mock_results = {
            "msisdn": "**********",
            "sim_profile": "DATA_ONLY",
            "msisdn_factor": "NATIONAL",
            "created_at": "2023-03-11 21:13:44",
            "uploaded_by": "User1",
            "country": "United Kingdom",
            "account_name": "Test Account",
            "logo_key": "/test/user/logo.png",
        }

        mock_msisdn_pool_details = MagicMock()
        func.msisdn_pool_details = mock_msisdn_pool_details

        mock_subquery = select(
            [
                literal_column("'**********'").label("msisdn"),
                literal_column("2023-03-11 21:13:44").label("created_at"),
                literal_column("User1").label("uploaded_by"),
                literal_column("'DATA_ONLY'").label("sim_profile"),
                literal_column("'NATIONAL'").label("msisdn_factor"),
                literal_column("'United Kingdom'").label("country"),
                literal_column("'Test Account'").label("account_name"),
                literal_column("'/test/user/logo.png'").label("logo_key"),
            ]
        )

        mock_msisdn_pool_details.return_value = mock_subquery
        expected_response = model.MsisdnDetails(**mock_results)
        mock_execute_result = MagicMock()
        mock_execute_result.all.return_value = [expected_response]
        session_mock.execute.return_value = mock_execute_result

        results = list(sim_repository.get_msisdn_pool_details())

        assert len(results) == 1
        assert isinstance(results[0], model.MsisdnDetails)
        assert results[0].msisdn == "**********"
        assert results[0].sim_profile == model.SimProfile.DATA_ONLY
        assert results[0].uploaded_by == "User1"

    def test_get_msisdn_pool_details_empty(self, sim_repository, session_mock):
        session_mock.execute.return_value.mappings.return_value.all.return_value = []

        results = list(sim_repository.get_msisdn_pool_details())

        assert isinstance(results, list)
        assert len(results) == 0

    def test_pool_msisdn_count(self, sim_repository, session_mock):
        session_mock.execute.return_value.scalar_one.return_value = 10

        result = sim_repository.pool_msisdn_count()

        assert isinstance(result, int)
        assert result == 10

    def test_get_msisdn_factor_success(self, sim_repository, session_mock):
        MockRow = namedtuple("Row", ["msisdn"])
        mock_result = [MockRow(msisdn="**********")]
        session_mock.execute.return_value.all.return_value = mock_result

        result = sim_repository.get_msisdn_factor(model.SimProfile.DATA_ONLY)

        assert result == ["**********"]
        session_mock.execute.assert_called_once()

    def test_get_msisdn_factor_no_result(self, sim_repository, session_mock):
        session_mock.execute.return_value.all.return_value = []

        result = sim_repository.get_msisdn_factor(model.SimProfile.DATA_ONLY)

        assert result == []
        session_mock.execute.assert_called_once()

    def test_bulk_update_sim_card_details_success(self, sim_repository, session_mock):
        sim_card_data = [
            model.SimCardData(
                requested_imsi="**********12345",
                existing_msisdn="9876543210",
                requested_msisdn="1112223333",
                allocation_id=1,
            ),
            model.SimCardData(
                requested_imsi="223456789012345",
                existing_msisdn="8876543210",
                requested_msisdn="2112223333",
                allocation_id=2,
            ),
        ]

        result = sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=sim_card_data, sim_profile=model.SimProfile.DATA_ONLY
        )

        assert session_mock.execute.call_count == 3

        session_mock.commit.assert_called_once()

        assert result is True

    def test_bulk_update_sim_card_details_integrity_error(
        self, sim_repository, session_mock
    ):
        sim_card_data = [
            model.SimCardData(
                requested_imsi="**********12345",
                existing_msisdn="9876543210",
                requested_msisdn="1112223333",
                allocation_id=1,
            )
        ]

        session_mock.execute.side_effect = IntegrityError("Mocked", {}, None)

        result = sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=sim_card_data, sim_profile=model.SimProfile.DATA_ONLY
        )

        session_mock.rollback.assert_called_once()

        session_mock.commit.assert_not_called()

        assert result is False

    def test_bulk_update_sim_card_details_success_sequence(
        self, sim_repository, session_mock
    ):
        sim_card_data = [
            model.SimCardData(
                requested_imsi="**********12345",
                existing_msisdn="9876543210",
                requested_msisdn="1112223333",
                allocation_id=1,
            ),
            model.SimCardData(
                requested_imsi="223456789012345",
                existing_msisdn="8876543210",
                requested_msisdn="2112223333",
                allocation_id=2,
            ),
        ]

        result = sim_repository.bulk_update_sim_card_details(
            all_sim_card_details=sim_card_data, sim_profile=model.SimProfile.DATA_ONLY
        )

        assert session_mock.execute.call_count == 3

        call_args = session_mock.execute.call_args_list

        sim_card_update_stmt = call_args[0][0][0]
        assert str(sim_card_update_stmt).startswith("UPDATE sim_card")

        clear_allocation_stmt = call_args[1][0][0]
        assert str(clear_allocation_stmt).startswith("UPDATE msisdn_pool")

        update_allocation_stmt = call_args[2][0][0]
        assert str(update_allocation_stmt).startswith("UPDATE msisdn_pool")

        session_mock.commit.assert_called_once()
        assert result is True

        assert "SET msisdn=" in str(sim_card_update_stmt)
        assert "SET allocation_id=" in str(clear_allocation_stmt)
        assert "SET sim_profile=" in str(update_allocation_stmt)

    def test_bulk_update_msisdn_pool_with_different_factors(
        self, sim_repository, session_mock
    ):
        imsis = ["**********12345", "234567890123456"]
        msisdns = ["9876543210", "8765432109"]
        sim_profile = model.SimProfile.DATA_ONLY
        msisdn_factor = model.MSISDNFactor.NATIONAL

        result = sim_repository.bulk_update_msisdn_pool(
            imsis=imsis,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            same_factor_allocation=False,
            msisdns=msisdns,
        )

        assert session_mock.execute.call_count == 2
        session_mock.commit.assert_called_once()
        assert result is True

    def test_bulk_update_msisdn_pool_with_same_factors(
        self, sim_repository, session_mock
    ):
        imsis = ["**********12345", "234567890123456"]
        sim_profile = model.SimProfile.DATA_ONLY
        msisdn_factor = model.MSISDNFactor.NATIONAL

        result = sim_repository.bulk_update_msisdn_pool(
            imsis=imsis,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            same_factor_allocation=True,
        )

        session_mock.execute.assert_called_once()
        session_mock.commit.assert_called_once()
        assert result is True

    def test_get_invalid_sim_and_msisdn_count(self, sim_repository, session_mock):
        mock_result = {
            "duplicate_iccids": 1,
            "duplicate_imsis": 10,
            "duplicate_msisdns": 1,
            "duplicate_msisdn_pool_msisdns": 11,
            "sim_card_msisdn_missing_msisdn_pool": 1,
            "sim_card_allocation_id_mismatch_msisdn_pool": 22,
            "create_range_count": 1,
            "add_msisdn_pool_count": 5,
            "update_sim_msisdn_count": 0,
        }

        session_mock.execute.return_value.one.return_value = mock_result

        result = sim_repository.get_invalid_sim_and_msisdn_count()

        session_mock.execute.assert_called_once()

        assert isinstance(result, model.InvalidSimMsisdnCountDetails)
        assert result.duplicate_iccids == 1
        assert result.duplicate_imsis == 10
        assert result.duplicate_msisdns == 1
        assert result.duplicate_msisdn_pool_msisdns == 11
        assert result.sim_card_msisdn_missing_msisdn_pool == 1
        assert result.sim_card_allocation_id_mismatch_msisdn_pool == 22
        assert result.create_range_count == 1
        assert result.add_msisdn_pool_count == 5
        assert result.update_sim_msisdn_count == 0

    def test_get_available_msisdn(self, sim_repository, session_mock):
        imsi_list = ["**********12345", "234567890123456"]
        msisdn_factor = model.MSISDNFactor.NATIONAL
        expected_msisdns = ["9876543210", "8765432109"]

        mock_scalar_result = MagicMock()
        mock_scalar_result.all.return_value = expected_msisdns

        session_mock.execute.return_value.scalars.return_value = mock_scalar_result

        result = sim_repository.get_available_msisdn(imsi_list, msisdn_factor)

        session_mock.execute.assert_called_once()
        assert result == expected_msisdns

    def test_get_available_msisdn_empty_result(self, sim_repository, session_mock):
        imsi_list = ["999999999999999"]
        msisdn_factor = model.MSISDNFactor.NATIONAL

        session_mock.execute.return_value.scalars.return_value.all.return_value = []

        result = sim_repository.get_available_msisdn(imsi_list, msisdn_factor)

        session_mock.execute.assert_called_once()
        assert result == []

    def test_validate_msisdn_update_request_valid(self, sim_repository, session_mock):
        msisdns = ["**********", "0987654321"]
        validation_key = "sim_profile"

        session_mock.execute.return_value.scalar_one.return_value = "DATA_ONLY"

        result = sim_repository.validate_msisdn_update_request(msisdns, validation_key)

        session_mock.execute.assert_called_once()
        assert result is True

    def test_validate_msisdn_update_request_invalid(self, sim_repository, session_mock):
        msisdns = ["**********", "0987654321"]
        validation_key = "sim_profile"

        session_mock.execute.return_value.scalar_one.return_value = None

        result = sim_repository.validate_msisdn_update_request(msisdns, validation_key)

        session_mock.execute.assert_called_once()
        assert result is None

    def test_validate_msisdn_update_request_sqlalchemy_error(
        self, sim_repository, session_mock
    ):
        msisdns = ["**********", "0987654321"]
        validation_key = "sim_profile"
        session_mock.execute.side_effect = IntegrityError("Mocked", {}, None)
        result = sim_repository.validate_msisdn_update_request(msisdns, validation_key)
        session_mock.execute.assert_called_once()
        assert result is False

    def test_validate_bulk_sim_card_details_valid(self, sim_repository, session_mock):
        sim_card_list = [{"IMSI": "**********12345", "MSISDN": "9876543210"}]
        valid_imsi_list = ["**********12345"]
        valid_msisdn_list = ["9876543210"]

        msisdn_results = [
            MagicMock(
                msisdn="9876543210", sim_profile="DATA_ONLY", msisdn_factor="NATIONAL"
            )
        ]
        sim_results = [
            MagicMock(imsi="**********12345", allocation_id=1, msisdn="9876543210")
        ]

        session_mock.execute.side_effect = [
            MagicMock(fetchall=MagicMock(return_value=msisdn_results)),
            MagicMock(fetchall=MagicMock(return_value=sim_results)),
        ]

        results = sim_repository.validate_bulk_sim_card_details(
            sim_card_list, valid_imsi_list, valid_msisdn_list
        )

        assert len(results) == 1
        result = results[0]

        assert result.requested_imsi == "**********12345"
        assert result.requested_msisdn == "9876543210"
        assert result.msisdn_sim_profile == "DATA_ONLY"
        assert result.msisdn_factor == "NATIONAL"
        assert result.existing_imsi == "**********12345"
        assert result.allocation_id == 1
        assert result.existing_msisdn == "9876543210"

    def test_validate_bulk_sim_card_details_missing_msisdn(
        self, sim_repository, session_mock
    ):
        sim_card_list = [{"IMSI": "999999999999999", "MSISDN": "1111111111"}]
        valid_imsi_list = ["999999999999999"]
        valid_msisdn_list = ["1111111111"]

        session_mock.execute.side_effect = [
            MagicMock(fetchall=MagicMock(return_value=[])),
            MagicMock(fetchall=MagicMock(return_value=[])),
        ]

        results = sim_repository.validate_bulk_sim_card_details(
            sim_card_list, valid_imsi_list, valid_msisdn_list
        )

        assert len(results) == 1
        result = results[0]
        assert result.msisdn_sim_profile is None
        assert result.existing_imsi is None
        assert result.allocation_id is None

    def test_validate_bulk_sim_card_details_multiple_entries(
        self, sim_repository, session_mock
    ):
        sim_card_list = [
            {"IMSI": "**********1234", "MSISDN": "**********"},
            {"IMSI": "**********1211", "MSISDN": "12345678111"},
        ]
        valid_imsi_list = ["**********1234", "**********1211"]
        valid_msisdn_list = ["**********", "12345678111"]

        msisdn_results = [
            MagicMock(
                msisdn="**********", sim_profile="DATA_ONLY", msisdn_factor="NATIONAL"
            ),
            MagicMock(
                msisdn="12345678111", sim_profile=None, msisdn_factor="INTERNATIONAL"
            ),
        ]
        sim_results = [
            MagicMock(imsi="**********1234", allocation_id=1, msisdn="**********"),
            MagicMock(imsi="**********1211", allocation_id=None, msisdn="12345678111"),
        ]

        session_mock.execute.side_effect = [
            MagicMock(fetchall=MagicMock(return_value=msisdn_results)),
            MagicMock(fetchall=MagicMock(return_value=sim_results)),
        ]

        results = sim_repository.validate_bulk_sim_card_details(
            sim_card_list, valid_imsi_list, valid_msisdn_list
        )

        assert len(results) == 2
        assert results[0].msisdn_sim_profile == "DATA_ONLY"
        assert results[1].msisdn_sim_profile is None
        assert results[0].allocation_id == 1
        assert results[1].allocation_id is None

    def test_upload_msisdn_success(self, sim_repository, session_mock):
        mock_msisdn_data = [MagicMock(), MagicMock()]

        sim_repository.upload_msisdn(mock_msisdn_data)

        session_mock.add_all.assert_called_once_with(mock_msisdn_data)
        session_mock.commit.assert_called_once()
        session_mock.rollback.assert_not_called()

    def test_upload_msisdn_empty_list(self, sim_repository, session_mock):
        sim_repository.upload_msisdn([])

        session_mock.add_all.assert_called_once_with([])
        session_mock.commit.assert_called_once()
        session_mock.rollback.assert_not_called()

    def test_unallocate_sim_cards_no_allocation_ids(self, sim_repository, session_mock):
        session_mock.query().filter().distinct().all.return_value = []

        result = sim_repository.unallocate_sim_cards(["111111111111111"])

        assert result.message == "0 out of 1 IMSIs were unallocated"

    def test_unallocate_sim_cards_success(self, sim_repository, session_mock):
        session_mock.execute.return_value.scalars.return_value.all.return_value = [1, 2]

        session_mock.query().filter().update.return_value = 2

        session_mock.query().filter().group_by().all.return_value = [(10, 2), (20, 1)]

        result = sim_repository.unallocate_sim_cards(
            ["111111111111111", "222222222222222"]
        )

        assert result.message == "2 out of 2 IMSIs unallocated"
        session_mock.commit.assert_called_once()

    def test_unallocate_sim_cards_empty_range_counts(
        self, sim_repository, session_mock
    ):
        session_mock.execute.return_value.scalars.return_value.all.return_value = [1]

        session_mock.query().filter().update.return_value = 1

        session_mock.query().filter().group_by().all.return_value = []

        result = sim_repository.unallocate_sim_cards(["111111111111111"])

        assert result.message == "1 out of 1 IMSIs unallocated"
        session_mock.commit.assert_called_once()

    def test_imsis_to_delete_success(self, sim_repository, session_mock):

        imsis_list = [***************, 600000000000002, 600000000000003]

        session_mock.query.return_value.filter.return_value.all.side_effect = [
            [
                (***************, None),
                (600000000000002, 123),
                (600000000000003, None),
            ],
            [(1,), (2,)],
        ]

        session_mock.query.return_value.filter.return_value.delete.return_value = None

        session_mock.query.return_value.filter.return_value.delete.return_value = 2

        range_row = MagicMock(id=10)
        session_mock.query.return_value.filter.return_value.first.return_value = (
            range_row
        )
        session_mock.query.return_value.filter.return_value.update.return_value = None

        result = sim_repository.imsis_to_delete(imsis_list)

        assert result["deleted_imsis_count"] == 2
        assert result["not_found_imsis_count"] == 0
        session_mock.commit.assert_called_once()

    def test_imsis_to_delete_sqlalchemy_error(self, sim_repository, session_mock):
        trace_id_var.set("12354")

        session_mock.query.side_effect = SQLAlchemyError("DB Error")

        with pytest.raises(SQLAlchemyError):
            sim_repository.imsis_to_delete([***************])


class TestCreateOrder:
    def test_create_order_success(self, sim_repository, session_mock):
        """Test successful order creation"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Test Customer",
                customer_account_name="Test Account",
                customer_account_id=1,
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test Street",
                city="Test City",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=50),
            ],
            order_status_history=model.OrderStatusHistory(status_name="PENDING"),
            notes="Test order notes",
        )

        # Mock the order creation
        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        # Mock the order data creation
        with patch("sim.adapters.repository.model.Order") as mock_order_class:
            mock_order_class.return_value = mock_order

            result = sim_repository.create_order(order_request)

            assert isinstance(result, model.OrderResponse)
            assert result.message == "Order created successfully"
            assert result.order_uuid == mock_order.uuid

            # Verify database operations
            session_mock.add.assert_called()
            session_mock.flush.assert_called_once()
            session_mock.commit.assert_called_once()

    def test_create_order_with_minimal_data(self, sim_repository, session_mock):
        """Test order creation with minimal required data"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Minimal Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Jane Doe", address_line1="456 Minimal Street"
            ),
            order_items=[model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=25)],
            order_status_history=model.OrderStatusHistory(),
        )

        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        with patch("sim.adapters.repository.model.Order") as mock_order_class:
            mock_order_class.return_value = mock_order

            result = sim_repository.create_order(order_request)

            assert isinstance(result, model.OrderResponse)
            assert result.message == "Order created successfully"
            session_mock.commit.assert_called_once()

    def test_create_order_empty_order_items(self, sim_repository, session_mock):
        """Test order creation with empty order items list"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Empty Items Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Empty Items Contact", address_line1="Empty Items Street"
            ),
            order_items=[],
            order_status_history=model.OrderStatusHistory(),
        )

        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        with patch("sim.adapters.repository.model.Order") as mock_order_class:
            mock_order_class.return_value = mock_order

            result = sim_repository.create_order(order_request)

            assert isinstance(result, model.OrderResponse)
            assert result.message == "Order created successfully"
            session_mock.commit.assert_called_once()

    def test_create_order_multiple_order_items(self, sim_repository, session_mock):
        """Test order creation with multiple order items"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Multi Items Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Multi Items Contact", address_line1="Multi Items Street"
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=500),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=300),
                model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=200),
            ],
            order_status_history=model.OrderStatusHistory(),
        )

        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        with patch("sim.adapters.repository.model.Order") as mock_order_class:
            mock_order_class.return_value = mock_order

            result = sim_repository.create_order(order_request)

            assert isinstance(result, model.OrderResponse)
            assert result.message == "Order created successfully"

            # Verify that add_all was called for order items (3 items)
            call_args_list = session_mock.add_all.call_args_list
            assert len(call_args_list) == 1
            # First call, first argument, first element
            added_items = call_args_list[0][0][0]
            assert len(added_items) == 3

    def test_create_order_sqlalchemy_error(self, sim_repository, session_mock):
        """Test order creation when SQLAlchemy error occurs"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Error Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Error Contact", address_line1="Error Street"
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_status_history=model.OrderStatusHistory(),
        )

        # Mock SQLAlchemy error during commit
        session_mock.commit.side_effect = SQLAlchemyError("Database connection error")

        with pytest.raises(SQLAlchemyError):
            sim_repository.create_order(order_request)

        # Verify rollback was called
        session_mock.rollback.assert_called_once()

    def test_create_order_with_notes(self, sim_repository, session_mock):
        """Test order creation with notes"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Notes Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Notes Contact", address_line1="Notes Street"
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_status_history=model.OrderStatusHistory(),
            notes="Special handling required for this order",
        )

        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        with patch("sim.adapters.repository.model.Order") as mock_order_class:
            mock_order_class.return_value = mock_order

            result = sim_repository.create_order(order_request)

            assert isinstance(result, model.OrderResponse)
            assert result.message == "Order created successfully"

            # Verify the order was created with notes
            mock_order_class.assert_called_once()
            call_kwargs = mock_order_class.call_args[1]
            assert call_kwargs["notes"] == "Special handling required for this order"

    def test_create_order_database_integrity_error(self, sim_repository, session_mock):
        """Test order creation when database integrity error occurs"""
        order_request = model.OrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Integrity Error Customer", customer_account_id=1
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Integrity Error Contact",
                address_line1="Integrity Error Street",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_status_history=model.OrderStatusHistory(),
        )

        # Mock integrity error during add
        session_mock.add.side_effect = IntegrityError("Constraint violation", {}, None)

        with pytest.raises(IntegrityError):
            sim_repository.create_order(order_request)

        # Verify rollback was called
        session_mock.rollback.assert_called_once()
