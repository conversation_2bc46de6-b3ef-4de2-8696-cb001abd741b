#!/bin/sh -e

# Set PYTHONPATH if 'src' folder exists
test ! -d src || export PYTHONPATH=${PYTHONPATH:-src}

###############################################################################
# Environment variables assignment and re-assignment
# Use the below link to find out variables set by deployment:
# https://gitlabnv2.flyaps.com/devops/deploy/-/tree/master/environments
# PLEASE UPDATE THE FOLLOWING VARS ACCORDINGLY TO YOUR APPLICATION REQUIREMENTS

export APPLICATION_ENV=${APP_ENV:-"prod"}
export PYTHON_BIN=${PYTHON_BIN:-"$(which python3|xargs realpath|xargs basename)"}
export GUNICORN_BIN=${GUNICORN_BIN:-"gunicorn"}
export APP_HOST=${APP_HOST:-"0.0.0.0"}
export APP_PORT=${APP_PORT:-"8000"}
export GUNICORN_WORKERS=${GUNICORN_WORKERS:-"4"}

DB_URI="${DB_SCHEME}://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
export DATABASE_URI=${SQLALCHEMY_DATABASE_URI:-"${DB_URI}"}

AUTH_URL_BASE=${AUTH_URL_BASE:-"https://${AUTH_HOST}/auth/realms/${AUTH_REALM}/protocol/openid-connect"}
export OIDC_CLIENT_ID=${OIDC_CLIENT_ID:-"monoglass"}
export OIDC_CLIENT_SECRET=${OIDC_CLIENT_SECRET:-"test_client_secret"}
export OIDC_AUTHORIZATION_URL="${AUTH_URL_BASE}/auth"
export OIDC_TOKEN_URL="${AUTH_URL_BASE}/token"
export OIDC_TOKEN_INTROSPECTION_URL="${AUTH_URL_BASE}/token/introspect"

CORE_NAMESPACE_SUFFIX=${ENV:+".cp-${ENV}-core.svc.cluster.local"}
export ORGANIZATIONS_URL=${ORGANIZATIONS_URL:-"http://nv2-core-organizations-api${CORE_NAMESPACE_SUFFIX}:8000/v1"}

# OpenTelemetry variables
USE_OTEL=${USE_OTEL:-0}
OTEL_INSTRUMENTATOR=${USE_OTEL:+"opentelemetry-instrument"}
export OTEL_SERVICE_NAME="monoglass"
export OTEL_PYTHON_LOG_CORRELATION=true
export OTEL_LOGS_EXPORTER="none"
export OTEL_TRACES_EXPORTER="none"
export OTEL_METRICS_EXPORTER="none"

# S3 variables
export S3_MEDIA_BUCKET_NAME="upload-dev"

export LANDING_PAGE_URL=${LANDING_PAGE_URL:-"https://${DOMAIN}"}

###############################################################################
# Service management commands implemented as functions start here
# Configure them as needed, but do NOT modify their names
###############################################################################

no_effect() {
	mcmd="${1}"
	echo "Management command '${mcmd}' has no effect for this service"
	return 0
}

render_dot_env() {
	rm -f .env
	VARS=$(egrep '^export' manage.sh | awk '{print $2}' | awk -F= '{print $1}')
	for V in ${VARS}; do echo "${V}='$(eval echo \"\$${V}\")'" >> .env; done
	echo "Created .env file"
}

init_db() {
	migrate_db
}

populate_db() {
	no_effect populate_db
}

migrate_db() {
	${PYTHON_BIN} -m alembic upgrade head
}

rollback_db() {
	rollback_db_revision="${1}"
	if test -z "${rollback_db_revision}"; then
		echo "ERROR: Management command 'rollback_db' requires revision id"
		exit 1
	fi
	${PYTHON_BIN} -m alembic downgrade "${rollback_db_revision}"
}


purge_db() {
	no_effect purge_db
}

run_cmd() {
	/bin/sh -ec "${@}"
}

alembic_last_history() {
	echo "alembic last revision details:- "
	${PYTHON_BIN} -m alembic history | head -n 10
}

###############################################################################
# Register URL with MANX PPL API
###############################################################################
register_notification_url(){
	${PYTHON_BIN} python -m script.registration --is-update=True
}

start_app() {
	$OTEL_INSTRUMENTATOR "${GUNICORN_BIN}" app.main:app -w "${GUNICORN_WORKERS}" -k uvicorn.workers.UvicornWorker -b "${APP_HOST}:${APP_PORT}"
}



start_service() {
	render_dot_env
	alembic_last_history
	migrate_db && start_app
}

###############################################################################
# Service management commands end here, no updates below this line
###############################################################################

manage_script="${0}"
if test "${#}" = "0"; then
	echo "ERROR: ${manage_script} requires at least one argument as management command"
	exit 1
fi
manage_command="${1}"
shift 1

case "${manage_command}" in
	start_service|start_app|run_cmd|init_db|populate_db|migrate_db|rollback_db|purge_db|render_dot_env|alembic_last_history)
		"${manage_command}" "${@}"
		;;
	*)
		echo "ERROR: unknown management command '${manage_command}'"
		exit 1
		;;
esac
